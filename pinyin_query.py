#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拼音查询工具
使用拼音key快速查询村居相邻关系
"""

import json
import sys
import argparse
from typing import Dict, List, Optional


class PinyinVillageQuery:
    """拼音村居查询器"""
    
    def __init__(self, pinyin_data_file: str = "villages_pinyin.json", 
                 index_file: str = "pinyin_index.json"):
        """
        初始化查询器
        
        Args:
            pinyin_data_file: 拼音数据文件路径
            index_file: 索引文件路径
        """
        self.pinyin_data_file = pinyin_data_file
        self.index_file = index_file
        self.pinyin_data = {}
        self.index_data = {}
        self.load_data()
    
    def load_data(self) -> bool:
        """加载数据文件"""
        try:
            # 加载拼音数据
            with open(self.pinyin_data_file, 'r', encoding='utf-8') as f:
                self.pinyin_data = json.load(f)
            
            # 加载索引数据
            with open(self.index_file, 'r', encoding='utf-8') as f:
                self.index_data = json.load(f)
            
            print(f"✅ 成功加载数据，共 {len(self.pinyin_data)} 个村居")
            return True
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    def query_by_pinyin(self, pinyin_key: str) -> Optional[Dict]:
        """
        通过拼音key查询村居信息
        
        Args:
            pinyin_key: 拼音key
            
        Returns:
            Dict: 村居信息，未找到返回None
        """
        return self.pinyin_data.get(pinyin_key.lower())
    
    def query_by_name(self, village_name: str) -> Optional[Dict]:
        """
        通过村居名称查询信息
        
        Args:
            village_name: 村居名称
            
        Returns:
            Dict: 村居信息，未找到返回None
        """
        pinyin_key = self.index_data.get('name_to_pinyin', {}).get(village_name)
        if pinyin_key:
            return self.query_by_pinyin(pinyin_key)
        return None
    
    def fuzzy_search(self, keyword: str) -> List[Dict]:
        """
        模糊搜索村居
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            List[Dict]: 匹配的村居列表
        """
        results = []
        keyword_lower = keyword.lower()
        
        for pinyin_key, village_data in self.pinyin_data.items():
            # 搜索拼音key
            if keyword_lower in pinyin_key:
                results.append(village_data)
                continue
            
            # 搜索村居名称
            if keyword in village_data['name']:
                results.append(village_data)
                continue
            
            # 搜索街道名称
            if keyword in village_data['street']:
                results.append(village_data)
                continue
        
        return results
    
    def print_village_info(self, village_data: Dict):
        """打印村居详细信息"""
        
        print(f"\n🏘️  村居信息")
        print("=" * 50)
        print(f"📍 村居名称: {village_data['name']}")
        print(f"🔤 拼音key: {village_data['pinyin']}")
        print(f"🏛️  所属街道: {village_data['street']}")
        print(f"🏙️  所属区域: {village_data['district']}")
        print(f"🔢 行政区划: {village_data['id']}")
        print(f"📐 村居面积: {village_data['area']:.2f}平方米")
        print(f"📏 村居周长: {village_data['perimeter']:.2f}米")
        print(f"🎯 中心坐标: ({village_data['coordinates']['center_x']:.2f}, {village_data['coordinates']['center_y']:.2f})")
        print(f"👥 相邻村居数: {village_data['neighbor_count']}")
        
        if village_data['neighbors']:
            print(f"\n📋 相邻村居列表:")
            print("-" * 50)
            
            # 按街道分组
            neighbors_by_street = {}
            for neighbor in village_data['neighbors']:
                street = neighbor['street']
                if street not in neighbors_by_street:
                    neighbors_by_street[street] = []
                neighbors_by_street[street].append(neighbor)
            
            for i, (street, street_neighbors) in enumerate(neighbors_by_street.items(), 1):
                print(f"\n{i}. {street} ({len(street_neighbors)}个):")
                for j, neighbor in enumerate(street_neighbors, 1):
                    print(f"   {i}.{j} {neighbor['name']} (拼音: {neighbor['pinyin']})")
        else:
            print("\n❌ 该村居没有相邻的村居")
    
    def list_all_pinyin_keys(self):
        """列出所有拼音key"""
        
        print(f"\n📋 所有村居拼音key列表 (共{len(self.pinyin_data)}个)")
        print("=" * 60)
        
        # 按拼音排序
        sorted_keys = sorted(self.pinyin_data.keys())
        
        for i, pinyin_key in enumerate(sorted_keys, 1):
            village_data = self.pinyin_data[pinyin_key]
            print(f"{i:3d}. {pinyin_key:<20} -> {village_data['name']} ({village_data['street']})")
    
    def show_statistics(self):
        """显示统计信息"""
        
        stats = self.index_data.get('statistics', {})
        
        print(f"\n📊 村居数据统计")
        print("=" * 40)
        print(f"📍 总村居数: {stats.get('total_villages', 0)}")
        print(f"🏛️  总街道数: {stats.get('total_streets', 0)}")
        print(f"👥 平均相邻数: {stats.get('avg_neighbors', 0):.1f}")
        
        # 显示一些示例
        print(f"\n🔤 拼音key示例:")
        sample_keys = list(self.pinyin_data.keys())[:10]
        for key in sample_keys:
            village_data = self.pinyin_data[key]
            print(f"   {key} -> {village_data['name']}")


def main():
    """主函数"""
    
    parser = argparse.ArgumentParser(
        description='拼音村居查询工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python pinyin_query.py dongheng
  python pinyin_query.py --name "东恒社区"
  python pinyin_query.py --search "永平"
  python pinyin_query.py --list
  python pinyin_query.py --stats
  python pinyin_query.py --interactive
        """
    )
    
    parser.add_argument(
        'pinyin_key',
        nargs='?',
        help='要查询的拼音key'
    )
    
    parser.add_argument(
        '-n', '--name',
        help='通过村居名称查询'
    )
    
    parser.add_argument(
        '-s', '--search',
        help='模糊搜索关键词'
    )
    
    parser.add_argument(
        '-l', '--list',
        action='store_true',
        help='列出所有拼音key'
    )
    
    parser.add_argument(
        '--stats',
        action='store_true',
        help='显示统计信息'
    )
    
    parser.add_argument(
        '-i', '--interactive',
        action='store_true',
        help='交互模式'
    )
    
    args = parser.parse_args()
    
    # 创建查询器
    query = PinyinVillageQuery()
    
    # 交互模式
    if args.interactive:
        interactive_mode(query)
        return
    
    # 显示统计信息
    if args.stats:
        query.show_statistics()
        return
    
    # 列出所有拼音key
    if args.list:
        query.list_all_pinyin_keys()
        return
    
    # 模糊搜索
    if args.search:
        results = query.fuzzy_search(args.search)
        if results:
            print(f"\n🔍 搜索 '{args.search}' 找到 {len(results)} 个结果:")
            for i, village_data in enumerate(results, 1):
                print(f"{i}. {village_data['name']} (拼音: {village_data['pinyin']}, {village_data['street']})")
        else:
            print(f"❌ 未找到包含 '{args.search}' 的村居")
        return
    
    # 通过名称查询
    if args.name:
        village_data = query.query_by_name(args.name)
        if village_data:
            query.print_village_info(village_data)
        else:
            print(f"❌ 未找到村居: {args.name}")
        return
    
    # 通过拼音key查询
    if args.pinyin_key:
        village_data = query.query_by_pinyin(args.pinyin_key)
        if village_data:
            query.print_village_info(village_data)
        else:
            print(f"❌ 未找到拼音key: {args.pinyin_key}")
        return
    
    # 没有提供参数，显示帮助
    parser.print_help()


def interactive_mode(query: PinyinVillageQuery):
    """交互模式"""
    
    print("\n" + "="*60)
    print("🔤 拼音村居查询工具 - 交互模式")
    print("="*60)
    print("💡 输入拼音key或村居名称查询，输入 'quit' 或 'q' 退出")
    print("💡 特殊命令:")
    print("   'list' - 显示所有拼音key")
    print("   'stats' - 显示统计信息")
    print("   'search 关键词' - 模糊搜索")
    
    while True:
        try:
            user_input = input("\n🔍 请输入查询内容: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 退出程序")
                break
            
            if not user_input:
                print("⚠️  请输入有效内容")
                continue
            
            if user_input.lower() == 'list':
                query.list_all_pinyin_keys()
                continue
            
            if user_input.lower() == 'stats':
                query.show_statistics()
                continue
            
            if user_input.lower().startswith('search '):
                keyword = user_input[7:].strip()
                if keyword:
                    results = query.fuzzy_search(keyword)
                    if results:
                        print(f"\n🔍 搜索 '{keyword}' 找到 {len(results)} 个结果:")
                        for i, village_data in enumerate(results, 1):
                            print(f"{i}. {village_data['name']} (拼音: {village_data['pinyin']})")
                    else:
                        print(f"❌ 未找到包含 '{keyword}' 的村居")
                continue
            
            # 尝试作为拼音key查询
            village_data = query.query_by_pinyin(user_input)
            if village_data:
                query.print_village_info(village_data)
                continue
            
            # 尝试作为村居名称查询
            village_data = query.query_by_name(user_input)
            if village_data:
                query.print_village_info(village_data)
                continue
            
            # 尝试模糊搜索
            results = query.fuzzy_search(user_input)
            if results:
                if len(results) == 1:
                    query.print_village_info(results[0])
                else:
                    print(f"\n🔍 找到 {len(results)} 个相关结果:")
                    for i, village_data in enumerate(results, 1):
                        print(f"{i}. {village_data['name']} (拼音: {village_data['pinyin']})")
            else:
                print(f"❌ 未找到相关村居: {user_input}")
            
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")


if __name__ == "__main__":
    main()
