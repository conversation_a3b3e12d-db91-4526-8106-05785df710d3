#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
村居分级防控管理系统 - 命令行工具
提供疫点管理、风险更新、分级查询等功能
"""

import argparse
import sys
import json
from datetime import datetime, date
from epidemic_control_system import (
    EpidemicControlSystem, VillageLevel, RiskLevel
)


def add_epidemic_point(system: EpidemicControlSystem, args):
    """添加疫点"""
    
    print(f"📍 添加疫点: {args.name}")
    print(f"   地址: {args.address}")
    
    # 如果提供了坐标，直接使用
    if args.coordinates:
        try:
            x, y = map(float, args.coordinates.split(','))
            coordinates = (x, y)
        except ValueError:
            print("❌ 坐标格式错误，应为: x,y")
            return
    else:
        # 通过地址查询坐标
        print("🔍 正在查询地址坐标...")
        coordinates = system.geo_calculator.query_coordinates_by_address(args.address)
        if not coordinates:
            print("❌ 无法获取地址坐标")
            return
    
    # 添加疫点
    success = system.add_epidemic_point(
        point_id=args.id,
        name=args.name,
        address=args.address,
        coordinates=coordinates,
        is_local=args.local
    )
    
    if success:
        # 显示影响统计
        stats = system.get_village_statistics()
        print(f"\n📊 疫点影响统计:")
        print(f"   一类村居: {stats['level_1']} 个")
        print(f"   二类村居: {stats['level_2']} 个")
        print(f"   三类村居: {stats['level_3']} 个")


def update_village_risk(system: EpidemicControlSystem, args):
    """更新村居风险"""
    
    # 查找村居
    village_id = None
    
    if args.village_id:
        village_id = args.village_id
    elif args.village_name:
        # 通过名称查找
        for vid, status in system.village_status.items():
            if args.village_name in status.village_name:
                village_id = vid
                break
    
    if not village_id:
        print(f"❌ 未找到村居: {args.village_name or args.village_id}")
        return
    
    # 解析风险等级
    risk_mapping = {
        'low': RiskLevel.LOW,
        'medium': RiskLevel.MEDIUM,
        'high': RiskLevel.HIGH,
        'controllable': RiskLevel.CONTROLLABLE
    }
    
    risk_level = risk_mapping.get(args.risk.lower())
    if not risk_level:
        print(f"❌ 无效的风险等级: {args.risk}")
        print("有效值: low, medium, high, controllable")
        return
    
    # 准备监测数据
    monitoring_data = {
        'update_time': datetime.now().isoformat(),
        'data_source': args.source or 'manual',
        'raw_data': args.data or {}
    }
    
    # 更新风险
    success = system.update_village_risk(
        village_id=village_id,
        risk_level=risk_level,
        monitoring_data=monitoring_data,
        notes=args.notes or ""
    )
    
    if success:
        status = system.village_status[village_id]
        print(f"✅ 已更新 {status.village_name} 的风险等级为: {risk_level.value}")
        
        # 显示防控措施
        measures = system.get_control_measures(village_id)
        print(f"\n📋 当前防控措施:")
        print(f"   分级: {measures['current_level']}")
        print(f"   监测频率: {measures['monitoring_frequency']}")
        print(f"   消杀频率: {measures['disinfection_frequency']}")


def query_village_status(system: EpidemicControlSystem, args):
    """查询村居状态"""
    
    if args.village_name:
        # 查询单个村居
        village_id = None
        for vid, status in system.village_status.items():
            if args.village_name in status.village_name:
                village_id = vid
                break
        
        if not village_id:
            print(f"❌ 未找到村居: {args.village_name}")
            return
        
        status = system.village_status[village_id]
        measures = system.get_control_measures(village_id)
        
        print(f"\n🏘️  村居状态详情")
        print("=" * 50)
        print(f"📍 村居名称: {status.village_name}")
        print(f"🏛️  当前分级: {status.current_level.value}")
        print(f"⚠️  当前风险: {status.current_risk.value}")
        print(f"📅 最后更新: {status.last_updated.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if status.affected_by_points:
            print(f"🦠 影响疫点: {', '.join(status.affected_by_points)}")
        
        print(f"\n📋 防控措施:")
        print(f"   监测频率: {measures['monitoring_frequency']}")
        print(f"   消杀频率: {measures['disinfection_frequency']}")
        print(f"   防控周期: {measures['control_duration']}")
        
        if measures['special_requirements']:
            print(f"   特殊要求:")
            for req in measures['special_requirements']:
                print(f"     - {req}")
        
        # 显示风险历史
        recent_risks = status.get_recent_risks(7)
        if recent_risks:
            print(f"\n📈 近7天风险记录:")
            for risk in recent_risks[-5:]:  # 显示最近5条
                print(f"   {risk.date}: {risk.risk_level.value}")
    
    elif args.level:
        # 查询指定分级的村居
        level_mapping = {
            '1': VillageLevel.LEVEL_1,
            '2': VillageLevel.LEVEL_2,
            '3': VillageLevel.LEVEL_3,
            'non': VillageLevel.NON_EPIDEMIC
        }
        
        level = level_mapping.get(args.level)
        if not level:
            print(f"❌ 无效的分级: {args.level}")
            print("有效值: 1, 2, 3, non")
            return
        
        villages = system.get_villages_by_level(level)
        
        print(f"\n📋 {level.value} 村居列表 (共{len(villages)}个)")
        print("-" * 80)
        
        for i, village in enumerate(villages, 1):
            print(f"{i:2d}. {village['name']:<20} {village['street']:<15} {village['current_risk']}")
    
    else:
        # 显示总体统计
        stats = system.get_village_statistics()
        
        print(f"\n📊 村居分级防控统计")
        print("=" * 40)
        print(f"总村居数: {stats['total_villages']}")
        print(f"活跃疫点: {stats['active_epidemic_points']}")
        print()
        print(f"分级分布:")
        print(f"  一类村居: {stats['level_1']} 个")
        print(f"  二类村居: {stats['level_2']} 个")
        print(f"  三类村居: {stats['level_3']} 个")
        print(f"  非涉疫村居: {stats['non_epidemic']} 个")
        print()
        print(f"风险分布:")
        print(f"  低风险: {stats['risk_distribution']['low']} 个")
        print(f"  中风险: {stats['risk_distribution']['medium']} 个")
        print(f"  高风险: {stats['risk_distribution']['high']} 个")
        print(f"  风险可控: {stats['risk_distribution']['controllable']} 个")


def show_high_risk_villages(system: EpidemicControlSystem, args):
    """显示高风险村居"""
    
    high_risk_villages = system.get_high_risk_villages()
    
    print(f"\n⚠️  高风险村居列表 (共{len(high_risk_villages)}个)")
    print("-" * 80)
    
    if not high_risk_villages:
        print("✅ 当前没有高风险村居")
        return
    
    for i, village in enumerate(high_risk_villages, 1):
        upgrade_status = "⬆️ 需要升级" if village['should_upgrade'] else ""
        print(f"{i:2d}. {village['name']:<20} {village['street']:<15} {village['current_level']} {upgrade_status}")
        print(f"    近期风险: {' -> '.join(village['recent_risks'])}")


def export_data(system: EpidemicControlSystem, args):
    """导出数据"""
    
    filename = args.output or f"epidemic_control_{date.today().strftime('%Y%m%d')}.json"
    
    exported_file = system.export_status_data(filename)
    
    if exported_file:
        print(f"✅ 数据已导出到: {exported_file}")
        
        # 显示导出统计
        stats = system.get_village_statistics()
        print(f"\n📊 导出数据统计:")
        print(f"   疫点数量: {stats['active_epidemic_points']}")
        print(f"   村居数量: {stats['total_villages']}")
        print(f"   一类村居: {stats['level_1']}")
        print(f"   二类村居: {stats['level_2']}")
        print(f"   三类村居: {stats['level_3']}")


def main():
    """主函数"""
    
    parser = argparse.ArgumentParser(
        description='村居分级防控管理系统',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 添加疫点
  python epidemic_control_cli.py add-point --id EP001 --name "某小区" --address "白云区东平北路11号" --local

  # 更新村居风险
  python epidemic_control_cli.py update-risk --village-name "平中社区" --risk high --notes "监测发现异常"

  # 查询村居状态
  python epidemic_control_cli.py query --village-name "平中社区"
  python epidemic_control_cli.py query --level 1

  # 显示高风险村居
  python epidemic_control_cli.py high-risk

  # 导出数据
  python epidemic_control_cli.py export --output control_data.json
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 添加疫点命令
    add_parser = subparsers.add_parser('add-point', help='添加疫点')
    add_parser.add_argument('--id', required=True, help='疫点ID')
    add_parser.add_argument('--name', required=True, help='疫点名称')
    add_parser.add_argument('--address', required=True, help='疫点地址')
    add_parser.add_argument('--coordinates', help='GZ2000坐标 (x,y)')
    add_parser.add_argument('--local', action='store_true', help='是否本地病例')
    
    # 更新风险命令
    risk_parser = subparsers.add_parser('update-risk', help='更新村居风险')
    risk_parser.add_argument('--village-id', help='村居ID')
    risk_parser.add_argument('--village-name', help='村居名称')
    risk_parser.add_argument('--risk', required=True, choices=['low', 'medium', 'high', 'controllable'], help='风险等级')
    risk_parser.add_argument('--source', help='数据来源')
    risk_parser.add_argument('--data', help='监测数据 (JSON格式)')
    risk_parser.add_argument('--notes', help='备注')
    
    # 查询命令
    query_parser = subparsers.add_parser('query', help='查询村居状态')
    query_parser.add_argument('--village-name', help='村居名称')
    query_parser.add_argument('--level', choices=['1', '2', '3', 'non'], help='查询指定分级的村居')
    
    # 高风险村居命令
    subparsers.add_parser('high-risk', help='显示高风险村居')
    
    # 导出命令
    export_parser = subparsers.add_parser('export', help='导出数据')
    export_parser.add_argument('--output', help='输出文件名')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 初始化系统
    print("🔄 正在初始化防控管理系统...")
    try:
        system = EpidemicControlSystem()
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        sys.exit(1)
    
    # 执行命令
    try:
        if args.command == 'add-point':
            add_epidemic_point(system, args)
        elif args.command == 'update-risk':
            update_village_risk(system, args)
        elif args.command == 'query':
            query_village_status(system, args)
        elif args.command == 'high-risk':
            show_high_risk_villages(system, args)
        elif args.command == 'export':
            export_data(system, args)
    
    except Exception as e:
        print(f"❌ 命令执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
