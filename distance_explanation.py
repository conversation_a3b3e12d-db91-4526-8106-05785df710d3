#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
距离说明示例 - 解释为什么会出现超过覆盖半径的村居
"""

from geo_coverage_calculator import GeoCoverageCalculator
from shapely.geometry import Point
import matplotlib.pyplot as plt
import matplotlib.patches as patches


def explain_distance_logic():
    """解释距离逻辑"""
    
    print("🔍 距离计算逻辑说明")
    print("=" * 50)
    
    # 模拟数据
    query_point = (42572.58, 243235.79)  # 东平北路11号
    village_center = (42883.53, 243564.83)  # 平中社区中心
    radius = 300
    
    # 计算中心点距离
    center_distance = ((village_center[0] - query_point[0])**2 + 
                      (village_center[1] - query_point[1])**2)**0.5
    
    print(f"📍 查询点坐标: ({query_point[0]}, {query_point[1]})")
    print(f"📍 村居中心坐标: ({village_center[0]}, {village_center[1]})")
    print(f"📏 中心点间距离: {center_distance:.2f}米")
    print(f"🎯 设定覆盖半径: {radius}米")
    print()
    
    print("💡 关键理解:")
    print("   1. 覆盖判断：300米圆形区域 与 村居边界多边形 是否相交")
    print("   2. 显示距离：查询点 到 村居中心点 的直线距离")
    print("   3. 这两个概念是不同的！")
    print()
    
    print("🎨 可能的情况:")
    print("   情况A: 村居很大，中心点远但边界近 → 相交但中心距离>半径")
    print("   情况B: 村居很小，中心点近且边界也近 → 相交且中心距离<半径")
    print("   情况C: 村居中等，部分边界在范围内 → 相交，中心距离可能>或<半径")
    print()


def detailed_analysis_example():
    """详细分析示例"""
    
    calculator = GeoCoverageCalculator()
    if not calculator.load_cunju_data():
        return
    
    # 查询东平北路11号的覆盖情况
    address = "东平北路 11 号"
    coordinates = calculator.query_coordinates_by_address(address)
    
    if not coordinates:
        return
    
    covered_villages = calculator.calculate_coverage(coordinates, radius=300)
    
    print("\n🔬 详细分析 - 为什么这些村居被包含在覆盖范围内:")
    print("=" * 60)
    
    for i, village in enumerate(covered_villages, 1):
        info = village['village_info']
        distance = village['distance_to_center']
        coverage_ratio = village['coverage_ratio']
        intersection_area = village['intersection_area']
        
        print(f"\n{i}. {info['NAME']}")
        print(f"   中心距离: {distance:.2f}米 {'(超出300米)' if distance > 300 else '(在300米内)'}")
        print(f"   覆盖比例: {coverage_ratio:.2%}")
        print(f"   相交面积: {intersection_area:.2f}平方米")
        
        if distance > 300:
            print(f"   ✅ 虽然中心点距离{distance:.0f}米 > 300米，但村居边界与300米圆形区域相交")
            print(f"   📐 这说明该村居面积较大，边界延伸到了300米范围内")
        else:
            print(f"   ✅ 中心点在300米范围内，且边界与圆形区域相交")
        
        # 估算村居大致尺寸
        village_area = village['village_area']
        estimated_radius = (village_area / 3.14159)**0.5  # 假设为圆形的等效半径
        print(f"   📏 村居面积: {village_area:.0f}平方米 (等效半径约{estimated_radius:.0f}米)")


def create_visual_explanation():
    """创建可视化说明（概念图）"""
    
    print("\n🎨 概念图说明:")
    print("=" * 30)
    print("假设有以下情况:")
    print()
    print("  查询点(Q) ●")
    print("             ╲")
    print("              ╲ 300米")
    print("               ╲")
    print("                ○ ← 300米圆形边界")
    print("                 ╲")
    print("                  ╲")
    print("                   ┌─────────────┐")
    print("                   │             │")
    print("                   │   村居A     │ ← 村居边界")
    print("                   │      ●      │ ← 村居中心(453米)")
    print("                   │             │")
    print("                   └─────────────┘")
    print()
    print("在这种情况下:")
    print("✅ 300米圆形区域与村居边界相交 → 算作覆盖")
    print("📏 但村居中心距离查询点453米 → 显示距离453米")


if __name__ == "__main__":
    explain_distance_logic()
    detailed_analysis_example()
    create_visual_explanation()
