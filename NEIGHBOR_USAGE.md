# 村居相邻关系分析工具使用指南

## 🎯 功能概述

村居相邻关系分析工具可以根据村居的地理位置信息，自动计算出每个村居的相邻村居列表。这对于城市规划、行政管理、应急响应等场景非常有用。

## 📊 分析结果

通过对375个村居的分析，我们发现：
- **总相邻关系数**: 1053对
- **平均相邻数**: 5.6个/村居
- **最多相邻数**: 17个（白云山风景区、江村）
- **最少相邻数**: 1个
- **孤立村居数**: 0个（所有村居都至少有1个相邻村居）

## 🚀 快速开始

### 1. 基本查询
```bash
# 查询单个村居的相邻关系
python neighbor_query.py "平中社区"

# 批量查询多个村居
python neighbor_query.py --batch "平中社区" "东恒社区" "飞鹅新村社区"
```

### 2. 统计信息
```bash
# 显示整体统计信息
python neighbor_query.py --stats

# 列出所有村居
python neighbor_query.py --list

# 按街道分组列出村居
python neighbor_query.py --list-by-street
```

### 3. 交互模式
```bash
# 启动交互模式
python neighbor_query.py --interactive
```

### 4. 数据导出
```bash
# 导出相邻关系数据到JSON文件
python neighbor_query.py --export my_neighbor_data.json
```

## 📋 命令行参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `village` | 要查询的村居名称 | `"平中社区"` |
| `-b, --batch` | 批量查询多个村居 | `--batch "村居1" "村居2"` |
| `-s, --stats` | 显示统计信息 | `--stats` |
| `-l, --list` | 列出所有村居 | `--list` |
| `--list-by-street` | 按街道分组列出村居 | `--list-by-street` |
| `-i, --interactive` | 交互模式 | `--interactive` |
| `-e, --export` | 导出数据到JSON文件 | `--export data.json` |
| `--no-cache` | 不使用缓存，重新计算 | `--no-cache` |

## 📖 使用示例

### 示例1：查询平中社区的相邻关系

```bash
python neighbor_query.py "平中社区"
```

**输出结果**：
```
🏘️  村居相邻关系分析
============================================================
📍 目标村居: 平中社区
🏛️  所属街道: 永平街
🏙️  所属区域: 白云区
🔢 行政区划: 440111011003
👥 相邻村居数量: 5

📋 相邻村居列表:
------------------------------------------------------------

1. 永平街 (2个村居):
   1.1 东恒社区 (行政区划: 440111011010)
   1.2 蝶云天社区 (行政区划: 440111011018)

2. 嘉禾街 (2个村居):
   2.1 双和社区 (行政区划: 440111012021)
   2.2 均禾社区 (行政区划: 440111012023)

3. 龙归街道 (1个村居):
   3.1 永兴村 (行政区划: 440111021220)

📊 统计信息:
   涉及街道数: 3
   同街道相邻村居: 2
   跨街道相邻村居: 3
```

### 示例2：查看整体统计

```bash
python neighbor_query.py --stats
```

**输出结果**：
```
📊 村居相邻关系统计报告
==================================================
📍 总村居数量: 375
🔗 总相邻关系数: 1053
📈 平均相邻数: 5.6
📊 最多相邻数: 17
📊 最少相邻数: 1
🏝️  孤立村居数: 0

🏆 相邻数最多的村居:
   白云山风景区 (17个)
   江村 (17个)
```

## 🔧 技术实现

### 相邻关系判断算法

系统使用多种几何算法来判断村居是否相邻：

1. **边界接触判断** (`touches()`): 检查两个多边形是否共享边界
2. **相交判断** (`intersects()`): 检查是否有任何形式的相交
3. **距离判断** (`distance()`): 距离小于1米认为相邻

### 性能优化

- **边界框预筛选**: 使用边界框快速排除不可能相邻的村居
- **几何数据修复**: 自动修复无效的几何数据
- **结果缓存**: 计算结果缓存到文件，避免重复计算
- **异常处理**: 完善的异常处理机制

## 📁 输出文件

### neighbor_relations.json
包含所有村居的相邻关系数据，格式如下：

```json
{
  "440111011003": {
    "village_info": {
      "id": "440111011003",
      "name": "平中社区",
      "street": "永平街",
      "district": "白云区"
    },
    "neighbors": [
      {
        "id": "440111011010",
        "name": "东恒社区",
        "street": "永平街",
        "district": "白云区"
      }
    ],
    "neighbor_count": 5
  }
}
```

### neighbor_cache.pkl
二进制缓存文件，包含计算结果和元数据，用于加速后续查询。

## 🎯 应用场景

1. **城市规划**: 分析村居间的空间关系，优化规划布局
2. **行政管理**: 了解行政区域的邻接关系，便于管理协调
3. **应急响应**: 快速确定事件影响的相邻区域
4. **服务网点**: 分析服务覆盖的相邻社区
5. **数据分析**: 为地理信息系统提供拓扑关系数据

## ⚠️ 注意事项

1. **几何数据质量**: 部分村居的几何数据可能存在拓扑错误，系统会自动尝试修复
2. **计算时间**: 首次运行需要计算所有相邻关系，可能需要几分钟时间
3. **缓存机制**: 系统会自动缓存计算结果，后续查询会很快
4. **内存使用**: 加载375个村居的几何数据需要一定内存空间

## 🔄 更新缓存

如果村居数据发生变化，需要重新计算相邻关系：

```bash
# 强制重新计算，不使用缓存
python neighbor_query.py --no-cache --stats

# 或者删除缓存文件
rm neighbor_cache.pkl
```

## 📞 技术支持

如果在使用过程中遇到问题，请检查：
1. 村居数据文件 `cunju.json` 是否存在
2. 是否安装了所需的Python依赖包
3. 几何数据是否存在严重错误

系统会自动处理大部分几何数据问题，并提供详细的错误信息。
