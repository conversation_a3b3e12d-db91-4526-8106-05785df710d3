# 拼音村居查询系统使用指南

## 🎯 系统概述

拼音村居查询系统将375个村居的中文名称转换为拼音key，实现快速查询和相邻关系分析。系统生成了两个核心数据文件：

- **villages_pinyin.json**: 以拼音为key的完整村居数据
- **pinyin_index.json**: 快速查询索引文件

## 📊 数据统计

- **总村居数**: 375个
- **总街道数**: 24个
- **平均相邻数**: 5.6个/村居
- **拼音冲突**: 8个（已自动处理）

## 🚀 快速开始

### 1. 生成拼音数据
```bash
# 生成拼音数据文件
python pinyin_preprocessor.py
```

### 2. 基本查询
```bash
# 通过拼音key查询
python pinyin_query.py dongheng

# 通过村居名称查询
python pinyin_query.py --name "东恒社区"

# 模糊搜索
python pinyin_query.py --search "永平"
```

### 3. 其他功能
```bash
# 显示统计信息
python pinyin_query.py --stats

# 列出所有拼音key
python pinyin_query.py --list

# 交互模式
python pinyin_query.py --interactive
```

## 📋 拼音key示例

| 拼音key | 村居名称 | 街道 |
|---------|----------|------|
| `dongheng` | 东恒社区 | 永平街 |
| `pingzhong` | 平中社区 | 永平街 |
| `junhe` | 均禾社区 | 嘉禾街 |
| `shuanghe` | 双和社区 | 嘉禾街 |
| `dieyuntian` | 蝶云天社区 | 永平街 |
| `jixianyuan` | 集贤苑社区 | 永平街 |
| `yongxing` | 永兴村 | 龙归街道 |
| `feiexin` | 飞鹅新村社区 | 三元里街 |
| `ziyuangang` | 梓元岗社区 | 三元里街 |
| `jinguiyuan` | 金桂园社区 | 三元里街 |

## 📖 使用示例

### 示例1：查询东恒社区

```bash
python pinyin_query.py dongheng
```

**输出结果**：
```
🏘️  村居信息
==================================================
📍 村居名称: 东恒社区
🔤 拼音key: dongheng
🏛️  所属街道: 永平街
🏙️  所属区域: 白云区
🔢 行政区划: 440111011010
📐 村居面积: 980451.34平方米
📏 村居周长: 4719.88米
🎯 中心坐标: (42213.70, 242735.30)
👥 相邻村居数: 4

📋 相邻村居列表:
--------------------------------------------------

1. 永平街 (3个):
   1.1 蝶云天社区 (拼音: dieyuntian)
   1.2 集贤苑社区 (拼音: jixianyuan)
   1.3 平中社区 (拼音: pingzhong)

2. 嘉禾街 (1个):
   2.1 均禾社区 (拼音: junhe)
```

### 示例2：模糊搜索永平街村居

```bash
python pinyin_query.py --search "永平"
```

**输出结果**：
```
🔍 搜索 '永平' 找到 18 个结果:
1. 元下田社区 (拼音: yuanxiatian, 永平街)
2. 新南庄社区 (拼音: xinnanzhuang, 永平街)
3. 平中社区 (拼音: pingzhong, 永平街)
4. 东恒社区 (拼音: dongheng, 永平街)
...
```

## 🔧 数据结构

### villages_pinyin.json 格式

```json
{
  "dongheng": {
    "id": "440111011010",
    "name": "东恒社区",
    "pinyin": "dongheng",
    "street": "永平街",
    "district": "白云区",
    "neighbors": [
      {
        "id": "440111011018",
        "name": "蝶云天社区",
        "pinyin": "dieyuntian",
        "street": "永平街",
        "district": "白云区"
      }
    ],
    "neighbor_count": 4,
    "coordinates": {
      "center_x": 42213.70,
      "center_y": 242735.30
    },
    "area": 980451.34,
    "perimeter": 4719.88
  }
}
```

### pinyin_index.json 格式

```json
{
  "pinyin_to_name": {
    "dongheng": "东恒社区"
  },
  "name_to_pinyin": {
    "东恒社区": "dongheng"
  },
  "street_villages": {
    "永平街": [
      {
        "name": "东恒社区",
        "pinyin": "dongheng",
        "neighbors": 4
      }
    ]
  },
  "all_pinyins": ["dongheng", "pingzhong", ...],
  "all_names": ["东恒社区", "平中社区", ...],
  "statistics": {
    "total_villages": 375,
    "total_streets": 24,
    "avg_neighbors": 5.6
  }
}
```

## 🔤 拼音转换规则

系统使用自定义的拼音映射表，包含：

1. **基础汉字**: 数字、方位、颜色等常用字
2. **地名词汇**: 社区、村、街、路、园、苑等
3. **特定地名**: 根据实际村居名称扩展的专用词汇

### 冲突处理

当多个村居生成相同拼音key时，系统会：
1. 检测冲突
2. 为冲突的村居添加街道标识
3. 重新生成唯一的拼音key

## 🎯 应用场景

1. **快速查询**: 通过简短的拼音key快速定位村居
2. **API接口**: 为前端应用提供简洁的查询接口
3. **数据分析**: 便于程序化处理和分析
4. **用户友好**: 支持拼音输入，降低使用门槛

## 📱 编程接口

### Python示例

```python
import json

# 加载数据
with open('villages_pinyin.json', 'r', encoding='utf-8') as f:
    villages = json.load(f)

# 查询东恒社区
dongheng = villages.get('dongheng')
if dongheng:
    print(f"村居: {dongheng['name']}")
    print(f"相邻数: {dongheng['neighbor_count']}")
    for neighbor in dongheng['neighbors']:
        print(f"  - {neighbor['name']} ({neighbor['pinyin']})")
```

### JavaScript示例

```javascript
// 加载数据
fetch('villages_pinyin.json')
  .then(response => response.json())
  .then(villages => {
    // 查询平中社区
    const pingzhong = villages['pingzhong'];
    if (pingzhong) {
      console.log(`村居: ${pingzhong.name}`);
      console.log(`相邻数: ${pingzhong.neighbor_count}`);
      pingzhong.neighbors.forEach(neighbor => {
        console.log(`  - ${neighbor.name} (${neighbor.pinyin})`);
      });
    }
  });
```

## ⚠️ 注意事项

1. **拼音准确性**: 部分复杂汉字可能转换不准确，已尽量优化
2. **冲突处理**: 8个拼音冲突已自动处理，添加了街道标识
3. **数据更新**: 如果村居数据变化，需要重新运行预处理器
4. **编码格式**: 所有文件使用UTF-8编码

## 🔄 数据更新流程

1. 更新村居原始数据 (`cunju.json`)
2. 重新运行相邻关系分析 (`python village_neighbor_analyzer.py`)
3. 重新生成拼音数据 (`python pinyin_preprocessor.py`)
4. 验证数据完整性 (`python pinyin_query.py --stats`)

## 📞 技术支持

系统提供完整的错误处理和用户友好的提示信息。如遇问题，请检查：
1. 数据文件是否存在且完整
2. 拼音key是否正确（区分大小写）
3. 搜索关键词是否准确

通过拼音查询系统，您可以快速、便捷地查询任何村居的详细信息和相邻关系！
