#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
村居拼音预处理器
将村居数据转换为以拼音为key的JSON格式，便于快速查询
"""

import json
import re
from typing import Dict, List
from village_neighbor_analyzer import VillageNeighborAnalyzer


# 拼音映射表 - 常用汉字到拼音的映射
PINYIN_MAP = {
    # 数字
    '一': 'yi', '二': 'er', '三': 'san', '四': 'si', '五': 'wu', '六': 'liu', '七': 'qi', '八': 'ba', '九': 'jiu', '十': 'shi',
    '第': 'di', '零': 'ling',

    # 方位
    '东': 'dong', '南': 'nan', '西': 'xi', '北': 'bei', '中': 'zhong', '上': 'shang', '下': 'xia', '左': 'zuo', '右': 'you',
    '前': 'qian', '后': 'hou', '内': 'nei', '外': 'wai',

    # 常用地名词汇
    '社区': 'shequ', '村': 'cun', '街': 'jie', '路': 'lu', '道': 'dao', '里': 'li', '园': 'yuan', '苑': 'yuan',
    '城': 'cheng', '镇': 'zhen', '区': 'qu', '县': 'xian', '市': 'shi', '省': 'sheng',
    '新': 'xin', '老': 'lao', '大': 'da', '小': 'xiao', '高': 'gao', '低': 'di', '长': 'chang', '短': 'duan',
    '花': 'hua', '山': 'shan', '水': 'shui', '河': 'he', '湖': 'hu', '海': 'hai', '桥': 'qiao', '岛': 'dao',
    '金': 'jin', '银': 'yin', '铜': 'tong', '铁': 'tie', '石': 'shi', '木': 'mu', '土': 'tu', '火': 'huo',
    '红': 'hong', '黄': 'huang', '蓝': 'lan', '绿': 'lv', '白': 'bai', '黑': 'hei', '紫': 'zi', '青': 'qing',
    '春': 'chun', '夏': 'xia', '秋': 'qiu', '冬': 'dong', '阳': 'yang', '阴': 'yin', '晴': 'qing', '雨': 'yu',

    # 具体地名（根据数据中的村居名称添加）
    '平': 'ping', '恒': 'heng', '飞': 'fei', '鹅': 'e', '梓': 'zi', '元': 'yuan', '岗': 'gang', '走': 'zou', '马': 'ma',
    '机场': 'jichang', '白云': 'baiyun', '松': 'song', '柏': 'bai', '医药': 'yiyao', '华': 'hua', '三': 'san',
    '里': 'li', '棠': 'tang', '景': 'jing', '百': 'bai', '顺': 'shun', '荣': 'rong', '溪': 'xi', '贝': 'bei',
    '岭': 'ling', '沙': 'sha', '涌': 'yong', '心': 'xin', '谊': 'yi', '边': 'bian', '合': 'he', '益': 'yi',
    '永': 'yong', '兴': 'xing', '集': 'ji', '贤': 'xian', '蝶': 'die', '云': 'yun', '天': 'tian', '双': 'shuang',
    '和': 'he', '均': 'jun', '禾': 'he', '龙': 'long', '归': 'gui', '政': 'zheng', '通': 'tong', '桂': 'gui',
    '萧': 'xiao', '得': 'de', '朋': 'peng', '连': 'lian', '桥': 'qiao', '望': 'wang', '嘉': 'jia', '尹': 'yin',
    '鹤': 'he', '彭': 'peng', '石': 'shi', '井': 'jing', '张': 'zhang', '潭': 'tan', '齐': 'qi', '富': 'fu',
    '乐': 'le', '黄': 'huang', '南': 'nan', '悦': 'yue', '镇': 'zhen', '湖': 'hu', '竹': 'zhu', '田': 'tian',
    '江': 'jiang', '中': 'zhong', '雄': 'xiong', '丰': 'feng', '井': 'jing', '罗': 'luo', '平': 'ping',
    '沙': 'sha', '何': 'he', '屋': 'wu', '倚': 'yi', '绿': 'lv', '庄': 'zhuang', '和': 'he', '花': 'hua',
    '祥': 'xiang', '梅': 'mei', '新': 'xin', '村': 'cun', '尚': 'shang', '夏': 'xia', '头': 'tou', '务': 'wu',
    '塘': 'tang', '月': 'yue', '色': 'se', '安': 'an', '金': 'jin', '钟': 'zhong', '风': 'feng', '区': 'qu',
    '高': 'gao', '尔': 'er', '夫': 'fu', '邦': 'bang', '陈': 'chen', '广': 'guang', '外': 'wai', '大': 'da',
    '学': 'xue', '荷': 'he', '期': 'qi', '榴': 'liu', '约': 'yue', '槎': 'cha', '团': 'tuan', '结': 'jie',
    '糖': 'tang', '半': 'ban', '螺': 'luo', '光': 'guang', '洲': 'zhou', '桃': 'tao', '力': 'li', '泰': 'tai',
    '翠': 'cui', '康': 'kang', '横': 'heng', '滘': 'jiao', '粤': 'yue', '掌': 'zhang', '坦': 'tan', '人': 'ren',
    '落': 'luo', '良': 'liang', '务': 'wu', '钟': 'zhong', '落': 'luo', '潭': 'tan', '良': 'liang', '田': 'tian',
    '沙': 'sha', '田': 'tian', '隆': 'long', '康': 'kang', '横': 'heng', '滘': 'jiao', '粤': 'yue', '溪': 'xi',
    '掌': 'zhang', '坦': 'tan', '人': 'ren', '和': 'he', '白': 'bai', '山': 'shan', '钟': 'zhong', '落': 'luo',
    '潭': 'tan', '良': 'liang', '田': 'tian', '沙': 'sha', '田': 'tian',
}


def chinese_to_pinyin(chinese_text: str) -> str:
    """
    将中文转换为拼音
    
    Args:
        chinese_text: 中文文本
        
    Returns:
        str: 拼音文本（小写，无分隔符）
    """
    # 移除常见的后缀词
    text = chinese_text.replace('社区', '').replace('村', '').replace('街道', '').replace('街', '')
    
    pinyin_parts = []
    i = 0
    
    while i < len(text):
        # 尝试匹配2个字符的词汇
        if i + 1 < len(text):
            two_char = text[i:i+2]
            if two_char in PINYIN_MAP:
                pinyin_parts.append(PINYIN_MAP[two_char])
                i += 2
                continue
        
        # 匹配单个字符
        char = text[i]
        if char in PINYIN_MAP:
            pinyin_parts.append(PINYIN_MAP[char])
        elif char.isalpha():
            # 如果是英文字母，直接添加
            pinyin_parts.append(char.lower())
        elif char.isdigit():
            # 如果是数字，直接添加
            pinyin_parts.append(char)
        # 忽略其他字符（如标点符号）
        
        i += 1
    
    return ''.join(pinyin_parts)


def generate_pinyin_key(village_name: str) -> str:
    """
    生成村居的拼音key
    
    Args:
        village_name: 村居名称
        
    Returns:
        str: 拼音key
    """
    # 基本拼音转换
    pinyin_key = chinese_to_pinyin(village_name)
    
    # 处理特殊情况
    if not pinyin_key:
        # 如果转换失败，使用原名称的字母和数字
        pinyin_key = re.sub(r'[^a-zA-Z0-9]', '', village_name.lower())
    
    # 确保key不为空
    if not pinyin_key:
        pinyin_key = 'unknown'
    
    return pinyin_key


def create_pinyin_neighbor_data(analyzer: VillageNeighborAnalyzer, output_file: str = "villages_pinyin.json") -> bool:
    """
    创建以拼音为key的村居相邻关系数据
    
    Args:
        analyzer: 村居相邻关系分析器
        output_file: 输出文件路径
        
    Returns:
        bool: 是否成功
    """
    if not analyzer.is_analyzed:
        print("请先分析相邻关系")
        return False
    
    pinyin_data = {}
    pinyin_conflicts = {}  # 记录拼音冲突
    
    print("正在生成拼音数据...")
    
    for village_id, village_data in analyzer.villages.items():
        village_info = village_data['info']
        village_name = village_info['NAME']
        
        # 生成拼音key
        pinyin_key = generate_pinyin_key(village_name)
        
        # 检查拼音冲突
        if pinyin_key in pinyin_data:
            if pinyin_key not in pinyin_conflicts:
                pinyin_conflicts[pinyin_key] = []
            pinyin_conflicts[pinyin_key].append(village_name)
            # 为冲突的村居添加后缀
            pinyin_key = f"{pinyin_key}_{village_info['PNAME'][:2]}"  # 添加街道前两个字
            pinyin_key = chinese_to_pinyin(pinyin_key)
        
        # 获取相邻村居信息
        neighbor_ids = analyzer.neighbor_relations.get(village_id, [])
        neighbors = []
        
        for neighbor_id in neighbor_ids:
            if neighbor_id in analyzer.villages:
                neighbor_data = analyzer.villages[neighbor_id]
                neighbor_info = neighbor_data['info']
                neighbor_pinyin = generate_pinyin_key(neighbor_info['NAME'])
                
                neighbors.append({
                    'id': neighbor_id,
                    'name': neighbor_info['NAME'],
                    'pinyin': neighbor_pinyin,
                    'street': neighbor_info['PNAME'],
                    'district': neighbor_info['PPNAME']
                })
        
        # 按拼音排序相邻村居
        neighbors.sort(key=lambda x: x['pinyin'])
        
        # 构建数据
        pinyin_data[pinyin_key] = {
            'id': village_id,
            'name': village_name,
            'pinyin': pinyin_key,
            'street': village_info['PNAME'],
            'district': village_info['PPNAME'],
            'neighbors': neighbors,
            'neighbor_count': len(neighbors),
            'coordinates': {
                'center_x': village_data['geometry']['center']['x'],
                'center_y': village_data['geometry']['center']['y']
            },
            'area': float(village_info['SmArea']),
            'perimeter': float(village_info['SmPerimeter'])
        }
    
    # 报告拼音冲突
    if pinyin_conflicts:
        print(f"\n⚠️  发现 {len(pinyin_conflicts)} 个拼音冲突:")
        for pinyin_key, village_names in pinyin_conflicts.items():
            print(f"   {pinyin_key}: {', '.join(village_names)}")
        print("已自动处理冲突（添加街道标识）")
    
    # 保存数据
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(pinyin_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 拼音数据已保存到 {output_file}")
        print(f"📊 总计 {len(pinyin_data)} 个村居")
        
        # 显示一些示例
        print(f"\n📋 拼音key示例:")
        sample_keys = list(pinyin_data.keys())[:10]
        for key in sample_keys:
            village_data = pinyin_data[key]
            print(f"   {key} -> {village_data['name']} ({village_data['street']})")
        
        return True
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return False


def create_pinyin_index(pinyin_data_file: str = "villages_pinyin.json", 
                       index_file: str = "pinyin_index.json") -> bool:
    """
    创建拼音索引文件，便于快速查找
    
    Args:
        pinyin_data_file: 拼音数据文件
        index_file: 索引文件路径
        
    Returns:
        bool: 是否成功
    """
    try:
        with open(pinyin_data_file, 'r', encoding='utf-8') as f:
            pinyin_data = json.load(f)
        
        # 创建多种索引
        index = {
            'pinyin_to_name': {},      # 拼音 -> 村居名称
            'name_to_pinyin': {},      # 村居名称 -> 拼音
            'street_villages': {},     # 街道 -> 村居列表
            'all_pinyins': [],         # 所有拼音key列表
            'all_names': [],           # 所有村居名称列表
            'statistics': {
                'total_villages': len(pinyin_data),
                'total_streets': 0,
                'avg_neighbors': 0
            }
        }
        
        total_neighbors = 0
        streets = set()
        
        for pinyin_key, village_data in pinyin_data.items():
            name = village_data['name']
            street = village_data['street']
            neighbor_count = village_data['neighbor_count']
            
            # 拼音索引
            index['pinyin_to_name'][pinyin_key] = name
            index['name_to_pinyin'][name] = pinyin_key
            index['all_pinyins'].append(pinyin_key)
            index['all_names'].append(name)
            
            # 街道索引
            if street not in index['street_villages']:
                index['street_villages'][street] = []
            index['street_villages'][street].append({
                'name': name,
                'pinyin': pinyin_key,
                'neighbors': neighbor_count
            })
            
            streets.add(street)
            total_neighbors += neighbor_count
        
        # 排序
        index['all_pinyins'].sort()
        index['all_names'].sort()
        
        # 统计信息
        index['statistics']['total_streets'] = len(streets)
        index['statistics']['avg_neighbors'] = total_neighbors / len(pinyin_data) if pinyin_data else 0
        
        # 保存索引
        with open(index_file, 'w', encoding='utf-8') as f:
            json.dump(index, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 拼音索引已保存到 {index_file}")
        return True
        
    except Exception as e:
        print(f"❌ 创建索引失败: {e}")
        return False


def main():
    """主函数"""
    print("🔄 开始生成村居拼音数据...")
    
    # 创建分析器
    analyzer = VillageNeighborAnalyzer()
    
    # 加载数据
    print("正在加载村居数据...")
    if not analyzer.load_cunju_data():
        print("❌ 加载村居数据失败")
        return
    
    # 分析相邻关系
    print("正在分析相邻关系...")
    analyzer.analyze_neighbor_relations()
    
    # 生成拼音数据
    print("\n正在生成拼音数据...")
    if create_pinyin_neighbor_data(analyzer):
        # 创建索引
        print("\n正在创建拼音索引...")
        create_pinyin_index()
        
        print("\n🎉 拼音数据生成完成！")
        print("\n📖 使用方法:")
        print("   1. 直接查询: villages_pinyin.json")
        print("   2. 索引查询: pinyin_index.json")
        print("   3. 示例: 'dongheng' -> 东恒社区")
    else:
        print("❌ 生成拼音数据失败")


if __name__ == "__main__":
    main()
