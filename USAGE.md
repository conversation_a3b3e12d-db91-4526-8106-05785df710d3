# 快速使用指南

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 基本使用

#### 方式一：命令行工具（推荐）
```bash
# 查询默认300米范围
python query_coverage.py "东平北路 11 号"

# 自定义半径
python query_coverage.py "东平北路 11 号" --radius 500

# 简化输出
python query_coverage.py "东平北路 11 号" --simple

# 交互模式
python query_coverage.py --interactive
```

#### 方式二：运行示例脚本
```bash
# 运行完整示例
python example_usage.py

# 交互模式
python example_usage.py --interactive
```

#### 方式三：直接运行主程序
```bash
python geo_coverage_calculator.py
```

## 📋 命令行参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `address` | 要查询的地址 | `"东平北路 11 号"` |
| `-r, --radius` | 覆盖半径（米），默认300 | `--radius 500` |
| `-i, --interactive` | 启动交互模式 | `--interactive` |
| `--simple` | 简化输出格式 | `--simple` |

## 💡 使用技巧

### 1. 地址输入建议
- ✅ 推荐：`"东平北路 11 号"`
- ✅ 推荐：`"白云区三元里大道"`
- ✅ 推荐：`"广州市白云区机场路"`
- ⚠️  注意：地址越具体，匹配度越高

### 2. 半径设置建议
- **200米**：小范围精确覆盖
- **300米**：默认推荐范围
- **500米**：中等范围覆盖
- **1000米**：大范围覆盖

### 3. 输出格式选择
- **详细模式**（默认）：显示完整的覆盖分析
- **简化模式**（`--simple`）：只显示关键信息

## 📊 输出结果说明

### 详细模式输出包含：
- 村居名称和行政信息
- 到中心点的距离
- 相交面积和村居总面积
- 覆盖比例
- 村居中心坐标

### 简化模式输出包含：
- 村居名称
- 所属街道
- 到中心距离
- 覆盖比例

## 🔧 常见问题

### Q: 查询失败怎么办？
A: 检查网络连接，确保能访问广州市地理信息API

### Q: 地址匹配度低怎么办？
A: 尝试使用更具体的地址，包含区、街道、门牌号等信息

### Q: 没有找到覆盖的村居？
A: 可能该地址周围指定范围内确实没有村居，或者地址查询失败

### Q: 坐标系统是什么？
A: 使用GZ2000坐标系，适用于广州地区

## 📁 文件说明

| 文件 | 说明 |
|------|------|
| `geo_coverage_calculator.py` | 核心计算器类 |
| `query_coverage.py` | 简单命令行工具 |
| `example_usage.py` | 使用示例和演示 |
| `cunju.json` | 村居地理数据 |
| `requirements.txt` | Python依赖包 |

## 🎯 实际应用场景

1. **城市规划**：分析某个地点的行政覆盖范围
2. **服务网点选址**：评估服务范围覆盖的社区
3. **应急响应**：快速确定事件影响的村居范围
4. **市场调研**：了解目标区域的行政划分
5. **政务服务**：确定管辖范围和服务对象

## 🔄 扩展开发

如需扩展功能，可以修改 `GeoCoverageCalculator` 类：

```python
from geo_coverage_calculator import GeoCoverageCalculator

# 创建自定义计算器
calculator = GeoCoverageCalculator()
calculator.load_cunju_data()

# 自定义查询
coordinates = calculator.query_coordinates_by_address("您的地址")
if coordinates:
    villages = calculator.calculate_coverage(coordinates, radius=您的半径)
    # 处理结果...
```
