#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
村居相邻关系分析器
根据村居地理位置信息计算村居之间的相邻关系
"""

import json
import time
from typing import List, Dict, Set, Tuple, Optional
from shapely.geometry import Point, Polygon
from shapely.prepared import prep
import pickle
import os


class VillageNeighborAnalyzer:
    """村居相邻关系分析器"""
    
    def __init__(self, cunju_file_path: str = "cunju.json", cache_file: str = "neighbor_cache.pkl"):
        """
        初始化分析器
        
        Args:
            cunju_file_path: 村居数据文件路径
            cache_file: 缓存文件路径
        """
        self.cunju_file_path = cunju_file_path
        self.cache_file = cache_file
        self.cunju_data = None
        self.villages = {}  # {village_id: village_data}
        self.neighbor_relations = {}  # {village_id: [neighbor_ids]}
        self.is_analyzed = False
        
    def load_cunju_data(self) -> bool:
        """
        加载村居数据
        
        Returns:
            bool: 加载是否成功
        """
        try:
            with open(self.cunju_file_path, 'r', encoding='utf-8') as f:
                self.cunju_data = json.load(f)
            
            # 解析村居数据
            features = self.cunju_data['recordsets'][0]['features']
            
            for feature in features:
                field_names = feature['fieldNames']
                field_values = feature['fieldValues']
                village_info = dict(zip(field_names, field_values))
                
                geometry = feature['geometry']
                points = geometry['points']
                
                # 创建多边形
                coords = [(point['x'], point['y']) for point in points]
                polygon = Polygon(coords)

                # 验证和修复几何数据
                if not polygon.is_valid:
                    print(f"警告: 村居 {village_info['NAME']} 的几何数据无效，尝试修复...")
                    try:
                        polygon = polygon.buffer(0)  # 尝试修复无效几何
                        if not polygon.is_valid:
                            print(f"错误: 无法修复村居 {village_info['NAME']} 的几何数据，跳过")
                            continue
                    except Exception as e:
                        print(f"错误: 修复村居 {village_info['NAME']} 几何数据时出错: {e}，跳过")
                        continue
                
                village_id = village_info['XZQH']  # 使用行政区划代码作为唯一标识
                
                self.villages[village_id] = {
                    'info': village_info,
                    'geometry': geometry,
                    'polygon': polygon,
                    'prepared_polygon': prep(polygon),  # 预处理多边形以提高性能
                    'bounds': polygon.bounds  # 边界框 (minx, miny, maxx, maxy)
                }
            
            print(f"成功加载村居数据，共 {len(self.villages)} 个村居")
            return True
            
        except Exception as e:
            print(f"加载村居数据失败: {e}")
            return False
    
    def _bounding_boxes_overlap(self, bounds1: Tuple[float, float, float, float], 
                               bounds2: Tuple[float, float, float, float]) -> bool:
        """
        检查两个边界框是否重叠
        
        Args:
            bounds1: 第一个边界框 (minx, miny, maxx, maxy)
            bounds2: 第二个边界框 (minx, miny, maxx, maxy)
            
        Returns:
            bool: 是否重叠
        """
        minx1, miny1, maxx1, maxy1 = bounds1
        minx2, miny2, maxx2, maxy2 = bounds2
        
        return not (maxx1 < minx2 or maxx2 < minx1 or maxy1 < miny2 or maxy2 < miny1)
    
    def _are_neighbors(self, village1_data: Dict, village2_data: Dict) -> bool:
        """
        判断两个村居是否相邻

        Args:
            village1_data: 第一个村居数据
            village2_data: 第二个村居数据

        Returns:
            bool: 是否相邻
        """
        polygon1 = village1_data['polygon']
        polygon2 = village2_data['polygon']

        try:
            # 方法1: 使用touches()判断是否共享边界
            if polygon1.touches(polygon2):
                return True

            # 方法2: 使用intersects()但排除包含关系
            if polygon1.intersects(polygon2):
                # 检查是否只是边界相交（不是一个包含另一个）
                intersection = polygon1.intersection(polygon2)
                # 如果相交部分的面积很小，认为是边界相邻
                if intersection.area < min(polygon1.area, polygon2.area) * 0.01:
                    return True

            # 方法3: 检查距离是否为0或很小
            distance = polygon1.distance(polygon2)
            if distance < 1.0:  # 距离小于1米认为相邻
                return True

        except Exception as e:
            # 如果几何计算出错，尝试使用距离判断
            try:
                distance = polygon1.distance(polygon2)
                if distance < 1.0:
                    return True
            except Exception:
                # 如果所有方法都失败，返回False
                pass

        return False
    
    def analyze_neighbor_relations(self, use_cache: bool = True) -> Dict[str, List[str]]:
        """
        分析所有村居的相邻关系
        
        Args:
            use_cache: 是否使用缓存
            
        Returns:
            Dict[str, List[str]]: 相邻关系字典
        """
        # 检查缓存
        if use_cache and os.path.exists(self.cache_file):
            try:
                with open(self.cache_file, 'rb') as f:
                    cached_data = pickle.load(f)
                    self.neighbor_relations = cached_data['neighbor_relations']
                    print(f"从缓存加载相邻关系数据，共 {len(self.neighbor_relations)} 个村居")
                    self.is_analyzed = True
                    return self.neighbor_relations
            except Exception as e:
                print(f"加载缓存失败: {e}，将重新计算")
        
        if not self.villages:
            print("请先加载村居数据")
            return {}
        
        print("开始分析村居相邻关系...")
        start_time = time.time()
        
        village_ids = list(self.villages.keys())
        total_comparisons = len(village_ids) * (len(village_ids) - 1) // 2
        completed_comparisons = 0
        
        # 初始化相邻关系字典
        for village_id in village_ids:
            self.neighbor_relations[village_id] = []
        
        # 计算相邻关系
        for i, village_id1 in enumerate(village_ids):
            village1_data = self.villages[village_id1]
            
            for j, village_id2 in enumerate(village_ids[i+1:], i+1):
                village2_data = self.villages[village_id2]
                
                # 边界框预筛选
                if not self._bounding_boxes_overlap(village1_data['bounds'], village2_data['bounds']):
                    completed_comparisons += 1
                    continue
                
                # 判断是否相邻
                if self._are_neighbors(village1_data, village2_data):
                    self.neighbor_relations[village_id1].append(village_id2)
                    self.neighbor_relations[village_id2].append(village_id1)
                
                completed_comparisons += 1
                
                # 显示进度
                if completed_comparisons % 1000 == 0:
                    progress = completed_comparisons / total_comparisons * 100
                    elapsed_time = time.time() - start_time
                    print(f"进度: {progress:.1f}% ({completed_comparisons}/{total_comparisons}), "
                          f"耗时: {elapsed_time:.1f}秒")
        
        elapsed_time = time.time() - start_time
        print(f"相邻关系分析完成，耗时: {elapsed_time:.1f}秒")
        
        # 统计结果
        total_relations = sum(len(neighbors) for neighbors in self.neighbor_relations.values()) // 2
        print(f"发现 {total_relations} 对相邻关系")
        
        # 保存缓存
        try:
            cache_data = {
                'neighbor_relations': self.neighbor_relations,
                'analysis_time': time.time(),
                'total_villages': len(self.villages),
                'total_relations': total_relations
            }
            with open(self.cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
            print(f"相邻关系数据已缓存到 {self.cache_file}")
        except Exception as e:
            print(f"保存缓存失败: {e}")
        
        self.is_analyzed = True
        return self.neighbor_relations
    
    def get_neighbors(self, village_id: str) -> List[Dict]:
        """
        获取指定村居的相邻村居列表
        
        Args:
            village_id: 村居ID（行政区划代码）
            
        Returns:
            List[Dict]: 相邻村居信息列表
        """
        if not self.is_analyzed:
            print("请先分析相邻关系")
            return []
        
        if village_id not in self.neighbor_relations:
            print(f"未找到村居 {village_id}")
            return []
        
        neighbor_ids = self.neighbor_relations[village_id]
        neighbors = []
        
        for neighbor_id in neighbor_ids:
            if neighbor_id in self.villages:
                neighbor_data = self.villages[neighbor_id]
                neighbors.append({
                    'village_id': neighbor_id,
                    'info': neighbor_data['info'],
                    'geometry': neighbor_data['geometry']
                })
        
        return neighbors
    
    def find_village_by_name(self, village_name: str) -> Optional[str]:
        """
        根据村居名称查找村居ID
        
        Args:
            village_name: 村居名称
            
        Returns:
            Optional[str]: 村居ID，未找到返回None
        """
        for village_id, village_data in self.villages.items():
            if village_data['info']['NAME'] == village_name:
                return village_id
        return None
    
    def get_neighbor_statistics(self) -> Dict:
        """
        获取相邻关系统计信息
        
        Returns:
            Dict: 统计信息
        """
        if not self.is_analyzed:
            return {}
        
        neighbor_counts = [len(neighbors) for neighbors in self.neighbor_relations.values()]
        total_relations = sum(neighbor_counts) // 2
        
        stats = {
            'total_villages': len(self.villages),
            'total_relations': total_relations,
            'avg_neighbors_per_village': sum(neighbor_counts) / len(neighbor_counts) if neighbor_counts else 0,
            'max_neighbors': max(neighbor_counts) if neighbor_counts else 0,
            'min_neighbors': min(neighbor_counts) if neighbor_counts else 0,
            'villages_with_no_neighbors': sum(1 for count in neighbor_counts if count == 0)
        }
        
        return stats

    def export_neighbor_relations(self, output_file: str = "neighbor_relations.json") -> bool:
        """
        导出相邻关系数据到JSON文件

        Args:
            output_file: 输出文件路径

        Returns:
            bool: 导出是否成功
        """
        if not self.is_analyzed:
            print("请先分析相邻关系")
            return False

        try:
            export_data = {}

            for village_id, neighbor_ids in self.neighbor_relations.items():
                village_info = self.villages[village_id]['info']

                neighbors_info = []
                for neighbor_id in neighbor_ids:
                    neighbor_info = self.villages[neighbor_id]['info']
                    neighbors_info.append({
                        'id': neighbor_id,
                        'name': neighbor_info['NAME'],
                        'street': neighbor_info['PNAME'],
                        'district': neighbor_info['PPNAME']
                    })

                export_data[village_id] = {
                    'village_info': {
                        'id': village_id,
                        'name': village_info['NAME'],
                        'street': village_info['PNAME'],
                        'district': village_info['PPNAME']
                    },
                    'neighbors': neighbors_info,
                    'neighbor_count': len(neighbors_info)
                }

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            print(f"相邻关系数据已导出到 {output_file}")
            return True

        except Exception as e:
            print(f"导出失败: {e}")
            return False

    def print_neighbor_analysis(self, village_identifier: str):
        """
        打印指定村居的相邻关系分析

        Args:
            village_identifier: 村居标识符（可以是ID或名称）
        """
        # 尝试作为ID查找
        village_id = village_identifier
        if village_id not in self.villages:
            # 尝试作为名称查找
            village_id = self.find_village_by_name(village_identifier)
            if not village_id:
                print(f"❌ 未找到村居: {village_identifier}")
                return

        village_data = self.villages[village_id]
        village_info = village_data['info']
        neighbors = self.get_neighbors(village_id)

        print(f"\n🏘️  村居相邻关系分析")
        print("=" * 60)
        print(f"📍 目标村居: {village_info['NAME']}")
        print(f"🏛️  所属街道: {village_info['PNAME']}")
        print(f"🏙️  所属区域: {village_info['PPNAME']}")
        print(f"🔢 行政区划: {village_info['XZQH']}")
        print(f"👥 相邻村居数量: {len(neighbors)}")

        if not neighbors:
            print("❌ 该村居没有相邻的村居")
            return

        print(f"\n📋 相邻村居列表:")
        print("-" * 60)

        # 按街道分组显示
        neighbors_by_street = {}
        for neighbor in neighbors:
            street = neighbor['info']['PNAME']
            if street not in neighbors_by_street:
                neighbors_by_street[street] = []
            neighbors_by_street[street].append(neighbor)

        for i, (street, street_neighbors) in enumerate(neighbors_by_street.items(), 1):
            print(f"\n{i}. {street} ({len(street_neighbors)}个村居):")
            for j, neighbor in enumerate(street_neighbors, 1):
                neighbor_info = neighbor['info']
                print(f"   {i}.{j} {neighbor_info['NAME']} (行政区划: {neighbor_info['XZQH']})")

        # 统计信息
        print(f"\n📊 统计信息:")
        print(f"   涉及街道数: {len(neighbors_by_street)}")
        print(f"   同街道相邻村居: {len([n for n in neighbors if n['info']['PNAME'] == village_info['PNAME']])}")
        print(f"   跨街道相邻村居: {len([n for n in neighbors if n['info']['PNAME'] != village_info['PNAME']])}")


def main():
    """主函数示例"""
    analyzer = VillageNeighborAnalyzer()

    # 加载数据
    if not analyzer.load_cunju_data():
        return

    # 分析相邻关系
    print("\n开始分析村居相邻关系...")
    neighbor_relations = analyzer.analyze_neighbor_relations()

    # 显示统计信息
    stats = analyzer.get_neighbor_statistics()
    print(f"\n📊 相邻关系统计:")
    print(f"   总村居数: {stats['total_villages']}")
    print(f"   总相邻关系数: {stats['total_relations']}")
    print(f"   平均每个村居的相邻数: {stats['avg_neighbors_per_village']:.1f}")
    print(f"   最多相邻数: {stats['max_neighbors']}")
    print(f"   最少相邻数: {stats['min_neighbors']}")
    print(f"   没有相邻村居的数量: {stats['villages_with_no_neighbors']}")

    # 示例查询
    test_villages = ["平中社区", "东恒社区", "飞鹅新村社区"]

    for village_name in test_villages:
        analyzer.print_neighbor_analysis(village_name)

    # 导出数据
    analyzer.export_neighbor_relations()


if __name__ == "__main__":
    main()
