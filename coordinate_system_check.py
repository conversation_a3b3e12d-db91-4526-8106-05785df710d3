#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标系统检查工具
验证GZ2000坐标系的距离计算准确性
"""

import json
import math
from typing import Tuple
from shapely.geometry import Point
from geo_coverage_calculator import GeoCoverageCalculator


def euclidean_distance(point1: Tuple[float, float], point2: Tuple[float, float]) -> float:
    """
    计算两点间的欧几里得距离（平面距离）
    
    Args:
        point1: 第一个点 (x, y)
        point2: 第二个点 (x, y)
        
    Returns:
        float: 距离（米）
    """
    dx = point2[0] - point1[0]
    dy = point2[1] - point1[1]
    return math.sqrt(dx * dx + dy * dy)


def analyze_coordinate_system():
    """分析坐标系统和距离计算"""

    print("🔍 GZ2000坐标系统分析")
    print("=" * 60)

    # 直接加载JSON数据
    try:
        with open('cunju.json', 'r', encoding='utf-8') as f:
            cunju_data = json.load(f)
    except Exception as e:
        print(f"加载数据失败: {e}")
        return

    features = cunju_data['recordsets'][0]['features']
    print(f"成功加载 {len(features)} 个村居数据")

    # 获取一些示例坐标
    sample_villages = []
    all_x = []
    all_y = []

    for i, feature in enumerate(features[:5]):
        field_names = feature['fieldNames']
        field_values = feature['fieldValues']
        village_info = dict(zip(field_names, field_values))

        center = feature['geometry']['center']
        sample_villages.append({
            'name': village_info['NAME'],
            'center': (center['x'], center['y'])
        })

        all_x.append(center['x'])
        all_y.append(center['y'])

    # 收集所有坐标用于范围分析
    for feature in features:
        center = feature['geometry']['center']
        all_x.append(center['x'])
        all_y.append(center['y'])

    print("📍 示例村居坐标 (GZ2000):")
    for village in sample_villages:
        x, y = village['center']
        print(f"   {village['name']}: ({x:.2f}, {y:.2f})")

    print(f"\n📏 坐标范围分析:")
    print(f"   X坐标范围: {min(all_x):.2f} ~ {max(all_x):.2f}")
    print(f"   Y坐标范围: {min(all_y):.2f} ~ {max(all_y):.2f}")
    print(f"   X跨度: {max(all_x) - min(all_x):.2f}米")
    print(f"   Y跨度: {max(all_y) - min(all_y):.2f}米")
    
    # 距离计算验证
    print(f"\n📐 距离计算验证:")
    
    # 选择两个村居进行距离计算
    village1 = sample_villages[0]
    village2 = sample_villages[1]
    
    # 使用欧几里得距离计算
    euclidean_dist = euclidean_distance(village1['center'], village2['center'])
    
    # 使用Shapely计算
    point1 = Point(village1['center'])
    point2 = Point(village2['center'])
    shapely_dist = point1.distance(point2)
    
    print(f"   {village1['name']} -> {village2['name']}")
    print(f"   欧几里得距离: {euclidean_dist:.2f}米")
    print(f"   Shapely距离: {shapely_dist:.2f}米")
    print(f"   差异: {abs(euclidean_dist - shapely_dist):.6f}米")
    
    # 验证300米圆形覆盖
    print(f"\n🎯 300米覆盖验证:")
    
    # 以第一个村居为中心，创建300米圆
    center_point = Point(village1['center'])
    circle_300m = center_point.buffer(300)
    
    print(f"   中心点: {village1['name']} ({village1['center'][0]:.2f}, {village1['center'][1]:.2f})")
    print(f"   300米圆面积: {circle_300m.area:.2f}平方米")
    print(f"   理论圆面积: {math.pi * 300 * 300:.2f}平方米")
    print(f"   面积差异: {abs(circle_300m.area - math.pi * 300 * 300):.2f}平方米")
    
    # 检查圆周上的点
    print(f"\n🔄 圆周采样验证:")
    angles = [0, 90, 180, 270]  # 四个方向
    
    for angle in angles:
        radian = math.radians(angle)
        x = village1['center'][0] + 300 * math.cos(radian)
        y = village1['center'][1] + 300 * math.sin(radian)
        
        # 验证距离
        actual_distance = euclidean_distance(village1['center'], (x, y))
        
        print(f"   {angle:3d}°方向: ({x:.2f}, {y:.2f}), 距离: {actual_distance:.2f}米")


def check_api_coordinates():
    """检查API返回的坐标"""

    print(f"\n🌐 API坐标验证:")
    print("=" * 40)

    # 已知的API返回示例
    api_example = {
        'address': '广州市白云区东平北路11号',
        'gz2000_x': 42572.5775980000,
        'gz2000_y': 243235.7930910000,
        'wgs84_lon': 113.30864852,
        'wgs84_lat': 23.25569948
    }

    print(f"地址: {api_example['address']}")
    print(f"GZ2000坐标: ({api_example['gz2000_x']}, {api_example['gz2000_y']})")
    print(f"WGS84坐标: ({api_example['wgs84_lon']}, {api_example['wgs84_lat']})")

    # 直接加载数据找最近村居
    try:
        with open('cunju.json', 'r', encoding='utf-8') as f:
            cunju_data = json.load(f)
    except Exception as e:
        print(f"加载数据失败: {e}")
        return

    features = cunju_data['recordsets'][0]['features']
    api_point = Point(api_example['gz2000_x'], api_example['gz2000_y'])

    min_distance = float('inf')
    nearest_village = None

    for feature in features:
        field_names = feature['fieldNames']
        field_values = feature['fieldValues']
        village_info = dict(zip(field_names, field_values))

        center = feature['geometry']['center']
        village_point = Point(center['x'], center['y'])
        distance = api_point.distance(village_point)

        if distance < min_distance:
            min_distance = distance
            nearest_village = {
                'info': village_info,
                'center': center
            }

    if nearest_village:
        info = nearest_village['info']
        center = nearest_village['center']
        print(f"\n最近村居: {info['NAME']} ({info['PNAME']})")
        print(f"村居中心: ({center['x']:.2f}, {center['y']:.2f})")
        print(f"距离: {min_distance:.2f}米")


def coordinate_system_summary():
    """坐标系统总结"""
    
    print(f"\n📋 坐标系统总结:")
    print("=" * 50)
    print("✅ 坐标系统: GZ2000 (广州2000)")
    print("✅ 坐标单位: 米 (投影坐标系)")
    print("✅ 距离计算: 欧几里得距离 (平面距离)")
    print("✅ 适用范围: 广州市及周边地区")
    print("✅ 精度评估: 在广州市范围内，平面距离计算误差很小")
    
    print(f"\n⚠️  注意事项:")
    print("1. GZ2000是投影坐标系，在广州地区精度很高")
    print("2. 使用平面距离计算是正确的，误差可忽略")
    print("3. 300米圆形覆盖计算是准确的")
    print("4. 不需要进行坐标系转换或大地测量计算")
    
    print(f"\n🎯 结论:")
    print("当前的距离计算方法是正确的，无需修改！")


def main():
    """主函数"""
    analyze_coordinate_system()
    check_api_coordinates()
    coordinate_system_summary()


if __name__ == "__main__":
    main()
