#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法对比：几何相交法 vs 圆周采样法
展示两种算法的差异和正确性
"""

import json
import math
from typing import List, Dict, Tuple
from shapely.geometry import Point, Polygon
from geo_coverage_calculator import GeoCoverageCalculator


class AlgorithmComparison:
    """算法对比类"""
    
    def __init__(self, cunju_file_path: str = "cunju.json"):
        self.cunju_file_path = cunju_file_path
        self.cunju_data = None
        self.load_cunju_data()
    
    def load_cunju_data(self):
        """加载村居数据"""
        try:
            with open(self.cunju_file_path, 'r', encoding='utf-8') as f:
                self.cunju_data = json.load(f)
            print(f"成功加载村居数据，共 {len(self.cunju_data['recordsets'][0]['features'])} 个村居")
        except Exception as e:
            print(f"加载村居数据失败: {e}")
    
    def create_polygon_from_points(self, points: List[Dict]) -> Polygon:
        """从点列表创建多边形"""
        coords = [(point['x'], point['y']) for point in points]
        return Polygon(coords)
    
    def old_algorithm_intersection(self, center_point: Tuple[float, float], radius: float = 300) -> List[Dict]:
        """
        旧算法：几何相交法
        """
        if not self.cunju_data:
            return []
        
        # 创建中心点和覆盖圆形区域
        center = Point(center_point[0], center_point[1])
        coverage_circle = center.buffer(radius)
        
        covered_villages = []
        features = self.cunju_data['recordsets'][0]['features']
        
        for feature in features:
            try:
                field_names = feature['fieldNames']
                field_values = feature['fieldValues']
                village_info = dict(zip(field_names, field_values))
                
                geometry = feature['geometry']
                points = geometry['points']
                village_polygon = self.create_polygon_from_points(points)
                
                # 检查是否相交
                if coverage_circle.intersects(village_polygon):
                    intersection = coverage_circle.intersection(village_polygon)
                    intersection_area = intersection.area
                    village_area = village_polygon.area
                    coverage_ratio = intersection_area / village_area if village_area > 0 else 0
                    
                    village_center = Point(geometry['center']['x'], geometry['center']['y'])
                    distance_to_center = center.distance(village_center)
                    
                    covered_info = {
                        'village_info': village_info,
                        'method': 'intersection',
                        'intersection_area': intersection_area,
                        'village_area': village_area,
                        'coverage_ratio': coverage_ratio,
                        'distance_to_center': distance_to_center,
                        'village_center': (geometry['center']['x'], geometry['center']['y'])
                    }
                    covered_villages.append(covered_info)
                    
            except Exception as e:
                continue
        
        covered_villages.sort(key=lambda x: x['distance_to_center'])
        return covered_villages
    
    def new_algorithm_sampling(self, center_point: Tuple[float, float], radius: float = 300, sample_points: int = 360) -> List[Dict]:
        """
        新算法：圆周采样法
        """
        if not self.cunju_data:
            return []
        
        # 生成圆周上的采样点
        sampling_points = []
        for i in range(sample_points):
            angle = 2 * math.pi * i / sample_points
            x = center_point[0] + radius * math.cos(angle)
            y = center_point[1] + radius * math.sin(angle)
            sampling_points.append(Point(x, y))
        
        # 创建村居多边形字典
        village_polygons = {}
        village_info_dict = {}
        
        features = self.cunju_data['recordsets'][0]['features']
        
        for feature in features:
            try:
                field_names = feature['fieldNames']
                field_values = feature['fieldValues']
                village_info = dict(zip(field_names, field_values))
                
                geometry = feature['geometry']
                points = geometry['points']
                village_polygon = self.create_polygon_from_points(points)
                
                village_id = village_info['XZQH']
                village_polygons[village_id] = village_polygon
                village_info_dict[village_id] = {
                    'info': village_info,
                    'geometry': geometry,
                    'polygon': village_polygon
                }
            except Exception as e:
                continue
        
        # 统计每个村居被采样到的点数
        village_hit_counts = {}
        
        for point in sampling_points:
            for village_id, polygon in village_polygons.items():
                try:
                    if polygon.contains(point):
                        if village_id not in village_hit_counts:
                            village_hit_counts[village_id] = 0
                        village_hit_counts[village_id] += 1
                        break
                except Exception as e:
                    continue
        
        # 构建结果列表
        covered_villages = []
        center = Point(center_point[0], center_point[1])
        
        for village_id, hit_count in village_hit_counts.items():
            village_data = village_info_dict[village_id]
            village_info = village_data['info']
            geometry = village_data['geometry']
            village_polygon = village_data['polygon']
            
            coverage_ratio = hit_count / sample_points
            village_center = Point(geometry['center']['x'], geometry['center']['y'])
            distance_to_center = center.distance(village_center)
            village_area = village_polygon.area
            
            covered_info = {
                'village_info': village_info,
                'method': 'sampling',
                'hit_count': hit_count,
                'sample_points': sample_points,
                'coverage_ratio': coverage_ratio,
                'distance_to_center': distance_to_center,
                'village_center': (geometry['center']['x'], geometry['center']['y']),
                'village_area': village_area
            }
            covered_villages.append(covered_info)
        
        covered_villages.sort(key=lambda x: (-x['coverage_ratio'], x['distance_to_center']))
        return covered_villages
    
    def compare_algorithms(self, address: str, radius: float = 300):
        """对比两种算法"""
        
        # 获取坐标
        calculator = GeoCoverageCalculator()
        coordinates = calculator.query_coordinates_by_address(address)
        
        if not coordinates:
            print("无法获取地址坐标")
            return
        
        print(f"\n{'='*80}")
        print(f"算法对比分析 - {address}")
        print(f"查询坐标: ({coordinates[0]:.2f}, {coordinates[1]:.2f})")
        print(f"覆盖半径: {radius}米")
        print(f"{'='*80}")
        
        # 运行两种算法
        old_results = self.old_algorithm_intersection(coordinates, radius)
        new_results = self.new_algorithm_sampling(coordinates, radius)
        
        print(f"\n📊 结果对比:")
        print(f"几何相交法找到: {len(old_results)} 个村居")
        print(f"圆周采样法找到: {len(new_results)} 个村居")
        
        # 详细对比
        print(f"\n🔍 详细对比:")
        print("-" * 80)
        
        # 获取所有涉及的村居
        old_villages = {v['village_info']['XZQH']: v for v in old_results}
        new_villages = {v['village_info']['XZQH']: v for v in new_results}
        
        all_village_ids = set(old_villages.keys()) | set(new_villages.keys())
        
        print(f"{'村居名称':<15} {'几何相交':<10} {'圆周采样':<10} {'距离(米)':<10} {'说明'}")
        print("-" * 80)
        
        for village_id in sorted(all_village_ids):
            old_village = old_villages.get(village_id)
            new_village = new_villages.get(village_id)
            
            if old_village and new_village:
                # 两种方法都找到
                name = old_village['village_info']['NAME']
                distance = old_village['distance_to_center']
                old_ratio = old_village['coverage_ratio']
                new_ratio = new_village['coverage_ratio']
                hit_count = new_village['hit_count']
                
                print(f"{name:<15} {old_ratio:>8.1%} {new_ratio:>8.1%} {distance:>8.0f} 两种方法都覆盖({hit_count}点)")
                
            elif old_village and not new_village:
                # 只有几何相交法找到
                name = old_village['village_info']['NAME']
                distance = old_village['distance_to_center']
                old_ratio = old_village['coverage_ratio']
                
                print(f"{name:<15} {old_ratio:>8.1%} {'--':>8} {distance:>8.0f} 仅几何相交(可能误判)")
                
            elif not old_village and new_village:
                # 只有圆周采样法找到
                name = new_village['village_info']['NAME']
                distance = new_village['distance_to_center']
                new_ratio = new_village['coverage_ratio']
                hit_count = new_village['hit_count']
                
                print(f"{name:<15} {'--':>8} {new_ratio:>8.1%} {distance:>8.0f} 仅圆周采样({hit_count}点)")
        
        # 分析差异
        only_old = set(old_villages.keys()) - set(new_villages.keys())
        only_new = set(new_villages.keys()) - set(old_villages.keys())
        
        print(f"\n📈 差异分析:")
        if only_old:
            print(f"仅几何相交法找到的村居 ({len(only_old)}个):")
            for village_id in only_old:
                village = old_villages[village_id]
                name = village['village_info']['NAME']
                distance = village['distance_to_center']
                print(f"  - {name} (距离{distance:.0f}米) - 可能是大村居边界擦边")
        
        if only_new:
            print(f"仅圆周采样法找到的村居 ({len(only_new)}个):")
            for village_id in only_new:
                village = new_villages[village_id]
                name = village['village_info']['NAME']
                distance = village['distance_to_center']
                hit_count = village['hit_count']
                print(f"  - {name} (距离{distance:.0f}米, {hit_count}个采样点) - 实际可达")
        
        print(f"\n💡 结论:")
        print(f"圆周采样法更准确地反映了从{address}出发{radius}米范围内实际能到达的村居")


def main():
    """主函数"""
    comparison = AlgorithmComparison()
    
    # 对比测试
    test_cases = [
        ("东平北路 11 号", 300),
        ("白云区三元里大道", 300),
        ("东平北路 11 号", 500),
    ]
    
    for address, radius in test_cases:
        comparison.compare_algorithms(address, radius)
        print("\n" + "="*80 + "\n")


if __name__ == "__main__":
    main()
