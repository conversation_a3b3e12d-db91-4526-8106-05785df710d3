#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
村居分级防控管理系统
基于地理位置和相邻关系的疫情防控分级管理
"""

import json
import datetime
from typing import Dict, List, Set, Optional, Tuple
from enum import Enum
from dataclasses import dataclass, asdict
from geo_coverage_calculator import GeoCoverageCalculator
from village_neighbor_analyzer import VillageNeighborAnalyzer


class VillageLevel(Enum):
    """村居分级"""
    LEVEL_1 = "一类村居"  # 出现本地病例或300米警戒区内
    LEVEL_2 = "二类村居"  # 与疫点相邻的村居
    LEVEL_3 = "三类村居"  # 未出现上述情形的村居
    NON_EPIDEMIC = "非涉疫村居"  # 非涉疫但需要防控的村居


class RiskLevel(Enum):
    """风险等级"""
    LOW = "低风险"
    MEDIUM = "中风险"
    HIGH = "高风险"
    CONTROLLABLE = "风险可控"


class MonitoringFrequency(Enum):
    """监测频率"""
    DAILY = "每日"
    EVERY_2_DAYS = "每2天"
    EVERY_3_DAYS = "每3天"
    WEEKLY = "每周"


class DisinfectionFrequency(Enum):
    """消杀频率"""
    DAILY = "每日"
    EVERY_2_DAYS = "每2天"
    EVERY_3_DAYS = "每3天"
    WEEKLY = "每周"
    IMMEDIATE = "立即开展"


@dataclass
class EpidemicPoint:
    """疫点信息"""
    id: str
    name: str
    address: str
    coordinates: Tuple[float, float]  # GZ2000坐标
    report_date: datetime.date
    is_local: bool = True  # 是否本地病例
    is_active: bool = True  # 是否活跃疫点


@dataclass
class RiskRecord:
    """风险记录"""
    date: datetime.date
    risk_level: RiskLevel
    monitoring_data: Dict  # 监测数据
    notes: str = ""


@dataclass
class VillageStatus:
    """村居状态"""
    village_id: str
    village_name: str
    current_level: VillageLevel
    current_risk: RiskLevel
    risk_history: List[RiskRecord]
    affected_by_points: List[str]  # 影响该村居的疫点ID列表
    last_updated: datetime.datetime
    control_start_date: Optional[datetime.date] = None  # 防控开始日期
    
    def add_risk_record(self, risk_level: RiskLevel, monitoring_data: Dict, notes: str = ""):
        """添加风险记录"""
        record = RiskRecord(
            date=datetime.date.today(),
            risk_level=risk_level,
            monitoring_data=monitoring_data,
            notes=notes
        )
        self.risk_history.append(record)
        self.current_risk = risk_level
        self.last_updated = datetime.datetime.now()
    
    def get_recent_risks(self, days: int = 7) -> List[RiskRecord]:
        """获取最近几天的风险记录"""
        cutoff_date = datetime.date.today() - datetime.timedelta(days=days)
        return [r for r in self.risk_history if r.date >= cutoff_date]
    
    def should_upgrade(self) -> bool:
        """判断是否应该升级"""
        recent_risks = self.get_recent_risks(days=5)
        if len(recent_risks) < 2:
            return False
        
        # 连续2次高风险
        if len(recent_risks) >= 2:
            last_two = recent_risks[-2:]
            if all(r.risk_level == RiskLevel.HIGH for r in last_two):
                return True
        
        # 连续3次中风险
        if len(recent_risks) >= 3:
            last_three = recent_risks[-3:]
            if all(r.risk_level == RiskLevel.MEDIUM for r in last_three):
                return True
        
        return False
    
    def should_downgrade(self) -> bool:
        """判断是否应该降级"""
        recent_risks = self.get_recent_risks(days=5)
        if len(recent_risks) < 2:
            return False
        
        # 连续2次风险可控
        last_two = recent_risks[-2:]
        return all(r.risk_level == RiskLevel.CONTROLLABLE for r in last_two)


class EpidemicControlSystem:
    """疫情防控管理系统"""
    
    def __init__(self):
        """初始化系统"""
        self.geo_calculator = GeoCoverageCalculator()
        self.neighbor_analyzer = VillageNeighborAnalyzer()
        
        # 数据存储
        self.epidemic_points: Dict[str, EpidemicPoint] = {}
        self.village_status: Dict[str, VillageStatus] = {}
        
        # 加载基础数据
        self._load_base_data()
    
    def _load_base_data(self):
        """加载基础地理数据"""
        print("正在加载基础地理数据...")
        
        # 加载村居数据
        if not self.geo_calculator.load_cunju_data():
            raise Exception("加载村居数据失败")
        
        # 加载相邻关系数据
        if not self.neighbor_analyzer.load_cunju_data():
            raise Exception("加载相邻关系数据失败")
        
        # 分析相邻关系
        self.neighbor_analyzer.analyze_neighbor_relations()
        
        # 初始化所有村居状态为三类村居
        for village_id, village_data in self.neighbor_analyzer.villages.items():
            village_info = village_data['info']
            self.village_status[village_id] = VillageStatus(
                village_id=village_id,
                village_name=village_info['NAME'],
                current_level=VillageLevel.LEVEL_3,
                current_risk=RiskLevel.LOW,
                risk_history=[],
                affected_by_points=[],
                last_updated=datetime.datetime.now()
            )
        
        print(f"✅ 成功初始化 {len(self.village_status)} 个村居的防控状态")
    
    def add_epidemic_point(self, point_id: str, name: str, address: str, 
                          coordinates: Tuple[float, float], is_local: bool = True) -> bool:
        """
        添加疫点
        
        Args:
            point_id: 疫点ID
            name: 疫点名称
            address: 疫点地址
            coordinates: GZ2000坐标
            is_local: 是否本地病例
            
        Returns:
            bool: 添加是否成功
        """
        try:
            epidemic_point = EpidemicPoint(
                id=point_id,
                name=name,
                address=address,
                coordinates=coordinates,
                report_date=datetime.date.today(),
                is_local=is_local
            )
            
            self.epidemic_points[point_id] = epidemic_point
            
            # 更新村居分级
            self._update_village_levels_for_point(epidemic_point)
            
            print(f"✅ 成功添加疫点: {name}")
            return True
            
        except Exception as e:
            print(f"❌ 添加疫点失败: {e}")
            return False
    
    def _update_village_levels_for_point(self, epidemic_point: EpidemicPoint):
        """根据疫点更新村居分级"""
        
        print(f"🔄 正在更新疫点 {epidemic_point.name} 影响的村居分级...")
        
        # 1. 计算300米警戒区内的村居 (一类村居)
        if epidemic_point.is_local:
            covered_villages = self.geo_calculator.calculate_coverage(
                epidemic_point.coordinates, radius=300
            )
            
            level_1_count = 0
            for village in covered_villages:
                village_id = village['village_info']['XZQH']
                if village_id in self.village_status:
                    status = self.village_status[village_id]
                    status.current_level = VillageLevel.LEVEL_1
                    status.affected_by_points.append(epidemic_point.id)
                    status.control_start_date = epidemic_point.report_date
                    level_1_count += 1
            
            print(f"   一类村居: {level_1_count} 个 (300米警戒区内)")
        
        # 2. 找出与一类村居相邻的村居 (二类村居)
        level_2_villages = set()
        
        for village_id, status in self.village_status.items():
            if status.current_level == VillageLevel.LEVEL_1:
                # 获取相邻村居
                neighbors = self.neighbor_analyzer.get_neighbors(village_id)
                for neighbor in neighbors:
                    neighbor_id = neighbor['village_id']
                    if (neighbor_id in self.village_status and 
                        self.village_status[neighbor_id].current_level not in [VillageLevel.LEVEL_1]):
                        level_2_villages.add(neighbor_id)
        
        # 更新二类村居状态
        for village_id in level_2_villages:
            status = self.village_status[village_id]
            status.current_level = VillageLevel.LEVEL_2
            if epidemic_point.id not in status.affected_by_points:
                status.affected_by_points.append(epidemic_point.id)
        
        print(f"   二类村居: {len(level_2_villages)} 个 (与一类村居相邻)")
        
        # 3. 其余村居保持三类或非涉疫状态
        level_3_count = sum(1 for s in self.village_status.values() 
                           if s.current_level == VillageLevel.LEVEL_3)
        
        print(f"   三类村居: {level_3_count} 个 (其他村居)")
    
    def update_village_risk(self, village_id: str, risk_level: RiskLevel, 
                           monitoring_data: Dict, notes: str = "") -> bool:
        """
        更新村居风险等级
        
        Args:
            village_id: 村居ID
            risk_level: 风险等级
            monitoring_data: 监测数据
            notes: 备注
            
        Returns:
            bool: 更新是否成功
        """
        if village_id not in self.village_status:
            print(f"❌ 村居ID不存在: {village_id}")
            return False
        
        status = self.village_status[village_id]
        old_level = status.current_level
        
        # 添加风险记录
        status.add_risk_record(risk_level, monitoring_data, notes)
        
        # 检查是否需要调整分级
        if status.should_upgrade():
            new_level = self._upgrade_level(status.current_level)
            if new_level != status.current_level:
                status.current_level = new_level
                print(f"⬆️  {status.village_name} 升级: {old_level.value} -> {new_level.value}")
        
        elif status.should_downgrade():
            new_level = self._downgrade_level(status.current_level)
            if new_level != status.current_level:
                status.current_level = new_level
                print(f"⬇️  {status.village_name} 降级: {old_level.value} -> {new_level.value}")
        
        return True
    
    def _upgrade_level(self, current_level: VillageLevel) -> VillageLevel:
        """升级分级"""
        if current_level == VillageLevel.LEVEL_3:
            return VillageLevel.LEVEL_2
        elif current_level == VillageLevel.LEVEL_2:
            return VillageLevel.LEVEL_1
        return current_level
    
    def _downgrade_level(self, current_level: VillageLevel) -> VillageLevel:
        """降级分级"""
        if current_level == VillageLevel.LEVEL_1:
            return VillageLevel.LEVEL_2
        elif current_level == VillageLevel.LEVEL_2:
            return VillageLevel.LEVEL_3
        return current_level

    def get_control_measures(self, village_id: str) -> Dict:
        """
        获取村居防控措施

        Args:
            village_id: 村居ID

        Returns:
            Dict: 防控措施详情
        """
        if village_id not in self.village_status:
            return {}

        status = self.village_status[village_id]
        level = status.current_level
        risk = status.current_risk

        measures = {
            'village_name': status.village_name,
            'current_level': level.value,
            'current_risk': risk.value,
            'monitoring_frequency': self._get_monitoring_frequency(level, risk),
            'disinfection_frequency': self._get_disinfection_frequency(level, risk),
            'special_requirements': self._get_special_requirements(level, status),
            'control_duration': self._get_control_duration(level, status)
        }

        return measures

    def _get_monitoring_frequency(self, level: VillageLevel, risk: RiskLevel) -> str:
        """获取蚊媒监测频率"""
        if level == VillageLevel.LEVEL_1:
            return MonitoringFrequency.DAILY.value
        elif level == VillageLevel.LEVEL_2:
            return MonitoringFrequency.EVERY_2_DAYS.value
        elif level == VillageLevel.LEVEL_3:
            return MonitoringFrequency.EVERY_3_DAYS.value
        else:
            return "根据风险等级确定"

    def _get_disinfection_frequency(self, level: VillageLevel, risk: RiskLevel) -> str:
        """获取消杀频率"""
        if level == VillageLevel.LEVEL_1:
            if risk in [RiskLevel.MEDIUM, RiskLevel.HIGH]:
                return DisinfectionFrequency.DAILY.value
            else:
                return DisinfectionFrequency.EVERY_2_DAYS.value
        elif level == VillageLevel.LEVEL_2:
            if risk in [RiskLevel.MEDIUM, RiskLevel.HIGH]:
                return DisinfectionFrequency.IMMEDIATE.value
            else:
                return DisinfectionFrequency.EVERY_3_DAYS.value
        elif level == VillageLevel.LEVEL_3:
            if risk in [RiskLevel.MEDIUM, RiskLevel.HIGH]:
                return DisinfectionFrequency.IMMEDIATE.value
            else:
                return DisinfectionFrequency.WEEKLY.value
        else:
            return "根据风险等级确定"

    def _get_special_requirements(self, level: VillageLevel, status: VillageStatus) -> List[str]:
        """获取特殊要求"""
        requirements = []

        if level == VillageLevel.LEVEL_1:
            requirements.extend([
                "疫情处置前5天全面彻底消杀",
                "应由外往内合围式推进",
                "9天后根据风险等级调整消杀频率"
            ])

        if status.control_start_date:
            days_since_start = (datetime.date.today() - status.control_start_date).days
            if days_since_start <= 5:
                requirements.append(f"处置期第{days_since_start}天，需全面消杀")
            elif days_since_start <= 9:
                requirements.append(f"处置期第{days_since_start}天，根据风险调整频率")

        return requirements

    def _get_control_duration(self, level: VillageLevel, status: VillageStatus) -> str:
        """获取防控周期"""
        if level == VillageLevel.LEVEL_1:
            return "至防控周期结束"
        elif level == VillageLevel.LEVEL_2:
            return "至邻近区域防控周期结束"
        else:
            return "持续监测"

    def get_village_statistics(self) -> Dict:
        """获取村居分级统计"""
        stats = {
            'total_villages': len(self.village_status),
            'level_1': 0,
            'level_2': 0,
            'level_3': 0,
            'non_epidemic': 0,
            'risk_distribution': {
                'low': 0,
                'medium': 0,
                'high': 0,
                'controllable': 0
            },
            'active_epidemic_points': len([p for p in self.epidemic_points.values() if p.is_active])
        }

        for status in self.village_status.values():
            if status.current_level == VillageLevel.LEVEL_1:
                stats['level_1'] += 1
            elif status.current_level == VillageLevel.LEVEL_2:
                stats['level_2'] += 1
            elif status.current_level == VillageLevel.LEVEL_3:
                stats['level_3'] += 1
            else:
                stats['non_epidemic'] += 1

            # 风险分布统计
            if status.current_risk == RiskLevel.LOW:
                stats['risk_distribution']['low'] += 1
            elif status.current_risk == RiskLevel.MEDIUM:
                stats['risk_distribution']['medium'] += 1
            elif status.current_risk == RiskLevel.HIGH:
                stats['risk_distribution']['high'] += 1
            else:
                stats['risk_distribution']['controllable'] += 1

        return stats

    def get_villages_by_level(self, level: VillageLevel) -> List[Dict]:
        """获取指定分级的村居列表"""
        villages = []

        for village_id, status in self.village_status.items():
            if status.current_level == level:
                village_data = self.neighbor_analyzer.villages[village_id]
                village_info = village_data['info']

                villages.append({
                    'village_id': village_id,
                    'name': status.village_name,
                    'street': village_info['PNAME'],
                    'district': village_info['PPNAME'],
                    'current_risk': status.current_risk.value,
                    'affected_by_points': status.affected_by_points,
                    'control_measures': self.get_control_measures(village_id)
                })

        return villages

    def get_high_risk_villages(self) -> List[Dict]:
        """获取高风险村居列表"""
        high_risk_villages = []

        for village_id, status in self.village_status.items():
            if status.current_risk == RiskLevel.HIGH:
                village_data = self.neighbor_analyzer.villages[village_id]
                village_info = village_data['info']

                high_risk_villages.append({
                    'village_id': village_id,
                    'name': status.village_name,
                    'street': village_info['PNAME'],
                    'district': village_info['PPNAME'],
                    'current_level': status.current_level.value,
                    'recent_risks': [r.risk_level.value for r in status.get_recent_risks(3)],
                    'should_upgrade': status.should_upgrade()
                })

        return high_risk_villages

    def export_status_data(self, filename: str = None) -> str:
        """导出状态数据"""
        if filename is None:
            filename = f"epidemic_control_status_{datetime.date.today().strftime('%Y%m%d')}.json"

        export_data = {
            'export_date': datetime.datetime.now().isoformat(),
            'epidemic_points': {},
            'village_status': {},
            'statistics': self.get_village_statistics()
        }

        # 导出疫点数据
        for point_id, point in self.epidemic_points.items():
            export_data['epidemic_points'][point_id] = {
                'id': point.id,
                'name': point.name,
                'address': point.address,
                'coordinates': point.coordinates,
                'report_date': point.report_date.isoformat(),
                'is_local': point.is_local,
                'is_active': point.is_active
            }

        # 导出村居状态数据
        for village_id, status in self.village_status.items():
            export_data['village_status'][village_id] = {
                'village_id': status.village_id,
                'village_name': status.village_name,
                'current_level': status.current_level.value,
                'current_risk': status.current_risk.value,
                'affected_by_points': status.affected_by_points,
                'last_updated': status.last_updated.isoformat(),
                'control_start_date': status.control_start_date.isoformat() if status.control_start_date else None,
                'risk_history': [
                    {
                        'date': r.date.isoformat(),
                        'risk_level': r.risk_level.value,
                        'monitoring_data': r.monitoring_data,
                        'notes': r.notes
                    } for r in status.risk_history
                ]
            }

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            print(f"✅ 状态数据已导出到: {filename}")
            return filename

        except Exception as e:
            print(f"❌ 导出失败: {e}")
            return ""
