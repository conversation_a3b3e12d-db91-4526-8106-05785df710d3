#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地理覆盖范围计算器使用示例
"""

from geo_coverage_calculator import GeoCoverageCalculator


def main():
    """主函数 - 演示各种使用方式"""
    
    # 创建计算器实例
    calculator = GeoCoverageCalculator()
    
    # 加载村居数据
    print("正在加载村居数据...")
    if not calculator.load_cunju_data():
        print("加载村居数据失败，程序退出")
        return
    
    print("\n" + "="*60)
    print("地理覆盖范围计算器 - 使用示例")
    print("="*60)
    
    # 示例1：查询指定地址的300米覆盖范围
    print("\n【示例1】查询指定地址的300米覆盖范围")
    test_addresses = [
        "东平北路 11 号",
        "白云区三元里大道",
        "广州市白云区机场路"
    ]
    
    for address in test_addresses:
        print(f"\n{'-'*50}")
        print(f"查询地址: {address}")
        covered_villages = calculator.calculate_coverage_by_address(address)
        
        if covered_villages:
            print(f"✓ 成功找到 {len(covered_villages)} 个覆盖的村居")
        else:
            print("✗ 未找到覆盖的村居或查询失败")
    
    # 示例2：自定义覆盖半径
    print(f"\n{'-'*50}")
    print("\n【示例2】自定义覆盖半径")
    test_address = "东平北路 11 号"
    
    for radius in [200, 500, 1000]:
        print(f"\n查询 {test_address} 周围 {radius}米 范围:")
        covered_villages = calculator.calculate_coverage_by_address(test_address, radius=radius)
        print(f"覆盖村居数量: {len(covered_villages)}")
    
    # 示例3：直接使用坐标计算
    print(f"\n{'-'*50}")
    print("\n【示例3】直接使用已知坐标计算")
    
    # 使用示例中的GZ2000坐标
    known_coordinates = (42572.58, 243235.79)
    print(f"使用坐标: {known_coordinates}")
    
    covered_villages = calculator.calculate_coverage(known_coordinates, radius=300)
    calculator.print_coverage_result(covered_villages, known_coordinates, radius=300)
    
    # 示例4：批量处理多个地址
    print(f"\n{'-'*50}")
    print("\n【示例4】批量处理多个地址")
    
    batch_addresses = [
        "白云区永平街",
        "白云区嘉禾街",
        "白云区三元里街"
    ]
    
    batch_results = {}
    
    for address in batch_addresses:
        print(f"\n处理地址: {address}")
        covered_villages = calculator.calculate_coverage_by_address(address, radius=500)
        batch_results[address] = covered_villages
    
    # 汇总批量处理结果
    print(f"\n{'-'*50}")
    print("批量处理结果汇总:")
    for address, villages in batch_results.items():
        print(f"  {address}: {len(villages)} 个村居")
    
    print(f"\n{'-'*50}")
    print("示例演示完成！")


def interactive_mode():
    """交互模式 - 让用户输入地址进行查询"""
    
    calculator = GeoCoverageCalculator()
    
    # 加载村居数据
    print("正在加载村居数据...")
    if not calculator.load_cunju_data():
        print("加载村居数据失败，程序退出")
        return
    
    print("\n" + "="*60)
    print("地理覆盖范围计算器 - 交互模式")
    print("="*60)
    print("输入地址查询300米覆盖范围，输入 'quit' 退出")
    
    while True:
        try:
            # 获取用户输入
            address = input("\n请输入地址: ").strip()
            
            if address.lower() in ['quit', 'exit', 'q']:
                print("退出程序")
                break
            
            if not address:
                print("请输入有效地址")
                continue
            
            # 询问是否自定义半径
            radius_input = input("请输入覆盖半径(米，默认300): ").strip()
            
            try:
                radius = int(radius_input) if radius_input else 300
                if radius <= 0:
                    print("半径必须大于0，使用默认值300米")
                    radius = 300
            except ValueError:
                print("半径格式错误，使用默认值300米")
                radius = 300
            
            # 执行查询
            print(f"\n正在查询 '{address}' 周围 {radius}米 范围...")
            covered_villages = calculator.calculate_coverage_by_address(address, radius=radius)
            
            if not covered_villages:
                print("未找到覆盖的村居或查询失败")
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        # 交互模式
        interactive_mode()
    else:
        # 示例模式
        main()
