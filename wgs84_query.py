#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WGS84坐标查询工具
查询村居的WGS84坐标信息
"""

import json
import math
import argparse
from typing import Dict, List, Tuple


def load_wgs84_data() -> Dict:
    """加载WGS84坐标数据"""
    try:
        with open('villages_with_wgs84.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载WGS84数据失败: {e}")
        return {}


def calculate_distance_wgs84(coord1: Tuple[float, float], coord2: Tuple[float, float]) -> float:
    """
    计算两个WGS84坐标点之间的距离 (使用Haversine公式)
    
    Args:
        coord1: (经度1, 纬度1)
        coord2: (经度2, 纬度2)
        
    Returns:
        float: 距离 (米)
    """
    lon1, lat1 = coord1
    lon2, lat2 = coord2
    
    # 转换为弧度
    lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
    
    # Haversine公式
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))
    
    # 地球半径 (米)
    r = 6371000
    
    return c * r


def query_village_wgs84(village_name: str, wgs84_data: Dict):
    """查询村居的WGS84坐标"""
    
    found_villages = []
    
    for village_id, data in wgs84_data.items():
        if village_name in data['name']:
            found_villages.append((village_id, data))
    
    if not found_villages:
        print(f"❌ 未找到村居: {village_name}")
        return
    
    if len(found_villages) == 1:
        village_id, data = found_villages[0]
        print_village_wgs84_info(village_id, data)
    else:
        print(f"🔍 找到 {len(found_villages)} 个匹配的村居:")
        for i, (village_id, data) in enumerate(found_villages, 1):
            print(f"{i}. {data['name']} ({data['street']}, {data['district']})")
        
        choice = input("请选择村居编号: ").strip()
        try:
            idx = int(choice) - 1
            if 0 <= idx < len(found_villages):
                village_id, data = found_villages[idx]
                print_village_wgs84_info(village_id, data)
            else:
                print("❌ 无效选择")
        except ValueError:
            print("❌ 输入格式错误")


def print_village_wgs84_info(village_id: str, data: Dict):
    """打印村居WGS84信息"""
    
    print(f"\n🏘️  村居WGS84坐标信息")
    print("=" * 50)
    print(f"📍 村居名称: {data['name']}")
    print(f"🏛️  所属街道: {data['street']}")
    print(f"🏙️  所属区域: {data['district']}")
    print(f"🔢 行政区划: {village_id}")
    
    gz2000 = data['coordinates']['gz2000']
    wgs84 = data['coordinates']['wgs84']
    accuracy = data['conversion_accuracy']
    
    print(f"\n📐 坐标信息:")
    print(f"   GZ2000: ({gz2000['x']:.2f}, {gz2000['y']:.2f})")
    print(f"   WGS84:  ({wgs84['longitude']:.6f}, {wgs84['latitude']:.6f})")
    
    print(f"\n🎯 转换精度:")
    print(f"   距参考点: {accuracy['distance_to_reference_m']:.0f}米")
    print(f"   估算误差: ±{accuracy['estimated_error_m']:.0f}米")
    print(f"   精度等级: {accuracy['accuracy_level']}")
    
    # 生成Google Maps链接
    google_maps_url = f"https://www.google.com/maps?q={wgs84['latitude']:.6f},{wgs84['longitude']:.6f}"
    print(f"\n🗺️  Google Maps: {google_maps_url}")


def find_nearby_villages_wgs84(target_lon: float, target_lat: float, radius_km: float, wgs84_data: Dict):
    """查找指定WGS84坐标附近的村居"""
    
    target_coord = (target_lon, target_lat)
    nearby_villages = []
    
    for village_id, data in wgs84_data.items():
        wgs84 = data['coordinates']['wgs84']
        village_coord = (wgs84['longitude'], wgs84['latitude'])
        
        distance = calculate_distance_wgs84(target_coord, village_coord)
        
        if distance <= radius_km * 1000:  # 转换为米
            nearby_villages.append({
                'id': village_id,
                'data': data,
                'distance': distance
            })
    
    # 按距离排序
    nearby_villages.sort(key=lambda x: x['distance'])
    
    print(f"\n🔍 在 ({target_lat:.6f}, {target_lon:.6f}) 周围 {radius_km}km 范围内找到 {len(nearby_villages)} 个村居:")
    print("-" * 80)
    
    for i, village in enumerate(nearby_villages, 1):
        data = village['data']
        distance = village['distance']
        accuracy = data['conversion_accuracy']
        
        print(f"{i:2d}. {data['name']:<20} {data['street']:<15} {distance:6.0f}米 (精度:{accuracy['accuracy_level']})")


def show_accuracy_statistics(wgs84_data: Dict):
    """显示转换精度统计"""
    
    print("\n📊 WGS84转换精度统计")
    print("=" * 40)
    
    accuracy_stats = {'high': [], 'medium': [], 'low': []}
    
    for village_id, data in wgs84_data.items():
        accuracy = data['conversion_accuracy']
        level = accuracy['accuracy_level']
        accuracy_stats[level].append({
            'name': data['name'],
            'distance': accuracy['distance_to_reference_m'],
            'error': accuracy['estimated_error_m']
        })
    
    print(f"总村居数: {len(wgs84_data)}")
    print()
    
    for level, villages in accuracy_stats.items():
        if not villages:
            continue
            
        level_name = {'high': '高精度', 'medium': '中精度', 'low': '低精度'}[level]
        print(f"🎯 {level_name} ({len(villages)}个):")
        
        distances = [v['distance'] for v in villages]
        errors = [v['error'] for v in villages]
        
        print(f"   距参考点: {min(distances):.0f} ~ {max(distances):.0f}米")
        print(f"   估算误差: {min(errors):.0f} ~ {max(errors):.0f}米")
        
        # 显示几个示例
        print(f"   示例村居:")
        for village in villages[:3]:
            print(f"     - {village['name']} (距{village['distance']:.0f}米, 误差±{village['error']:.0f}米)")
        print()


def interactive_query():
    """交互式查询"""
    
    print("🔍 WGS84坐标交互式查询")
    print("=" * 40)
    
    wgs84_data = load_wgs84_data()
    if not wgs84_data:
        return
    
    while True:
        print(f"\n选择功能:")
        print("1. 查询村居WGS84坐标")
        print("2. 查找附近村居")
        print("3. 显示精度统计")
        print("4. 退出")
        
        choice = input("请选择 (1-4): ").strip()
        
        if choice == '1':
            village_name = input("请输入村居名称: ").strip()
            if village_name:
                query_village_wgs84(village_name, wgs84_data)
        
        elif choice == '2':
            try:
                lat = float(input("请输入纬度: "))
                lon = float(input("请输入经度: "))
                radius = float(input("请输入搜索半径(km): "))
                
                find_nearby_villages_wgs84(lon, lat, radius, wgs84_data)
                
            except ValueError:
                print("❌ 输入格式错误")
        
        elif choice == '3':
            show_accuracy_statistics(wgs84_data)
        
        elif choice == '4':
            print("👋 退出查询")
            break
        
        else:
            print("❌ 无效选择")


def main():
    """主函数"""
    
    parser = argparse.ArgumentParser(description='WGS84坐标查询工具')
    parser.add_argument('--query', help='查询村居名称')
    parser.add_argument('--nearby', nargs=3, metavar=('LAT', 'LON', 'RADIUS'), 
                       help='查找附近村居 (纬度 经度 半径km)')
    parser.add_argument('--stats', action='store_true', help='显示精度统计')
    parser.add_argument('--interactive', action='store_true', help='交互式查询')
    
    args = parser.parse_args()
    
    print("🌍 WGS84坐标查询工具")
    print("=" * 30)
    
    wgs84_data = load_wgs84_data()
    if not wgs84_data:
        return
    
    if args.query:
        query_village_wgs84(args.query, wgs84_data)
    
    elif args.nearby:
        try:
            lat, lon, radius = map(float, args.nearby)
            find_nearby_villages_wgs84(lon, lat, radius, wgs84_data)
        except ValueError:
            print("❌ 坐标格式错误")
    
    elif args.stats:
        show_accuracy_statistics(wgs84_data)
    
    elif args.interactive:
        interactive_query()
    
    else:
        # 默认显示统计信息
        show_accuracy_statistics(wgs84_data)


if __name__ == "__main__":
    main()
