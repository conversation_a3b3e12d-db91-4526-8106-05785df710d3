#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
村居相邻关系查询工具
提供命令行接口查询村居的相邻关系
"""

import sys
import argparse
from village_neighbor_analyzer import VillageNeighborAnalyzer


def query_neighbors(analyzer: VillageNeighborAnalyzer, village_identifier: str):
    """查询指定村居的相邻关系"""
    analyzer.print_neighbor_analysis(village_identifier)


def batch_query_neighbors(analyzer: VillageNeighborAnalyzer, village_identifiers: list):
    """批量查询村居相邻关系"""
    
    print(f"\n🔍 批量查询 {len(village_identifiers)} 个村居的相邻关系:")
    print("=" * 80)
    
    for i, village_identifier in enumerate(village_identifiers, 1):
        print(f"\n【{i}/{len(village_identifiers)}】")
        analyzer.print_neighbor_analysis(village_identifier)
        
        if i < len(village_identifiers):
            print("\n" + "-" * 80)


def show_statistics(analyzer: VillageNeighborAnalyzer):
    """显示相邻关系统计信息"""
    
    stats = analyzer.get_neighbor_statistics()
    
    print(f"\n📊 村居相邻关系统计报告")
    print("=" * 50)
    print(f"📍 总村居数量: {stats['total_villages']}")
    print(f"🔗 总相邻关系数: {stats['total_relations']}")
    print(f"📈 平均相邻数: {stats['avg_neighbors_per_village']:.1f}")
    print(f"📊 最多相邻数: {stats['max_neighbors']}")
    print(f"📊 最少相邻数: {stats['min_neighbors']}")
    print(f"🏝️  孤立村居数: {stats['villages_with_no_neighbors']}")
    
    # 找出相邻数最多的村居
    if analyzer.neighbor_relations:
        max_neighbors_villages = []
        max_count = stats['max_neighbors']
        
        for village_id, neighbors in analyzer.neighbor_relations.items():
            if len(neighbors) == max_count:
                village_info = analyzer.villages[village_id]['info']
                max_neighbors_villages.append(f"{village_info['NAME']} ({len(neighbors)}个)")
        
        print(f"\n🏆 相邻数最多的村居:")
        for village in max_neighbors_villages[:5]:  # 显示前5个
            print(f"   {village}")
        
        # 找出孤立的村居
        if stats['villages_with_no_neighbors'] > 0:
            isolated_villages = []
            for village_id, neighbors in analyzer.neighbor_relations.items():
                if len(neighbors) == 0:
                    village_info = analyzer.villages[village_id]['info']
                    isolated_villages.append(f"{village_info['NAME']} ({village_info['PNAME']})")
            
            print(f"\n🏝️  孤立村居列表:")
            for village in isolated_villages[:10]:  # 显示前10个
                print(f"   {village}")


def list_all_villages(analyzer: VillageNeighborAnalyzer, by_street: bool = False):
    """列出所有村居"""
    
    if by_street:
        # 按街道分组显示
        villages_by_street = {}
        for village_id, village_data in analyzer.villages.items():
            street = village_data['info']['PNAME']
            if street not in villages_by_street:
                villages_by_street[street] = []
            villages_by_street[street].append(village_data['info'])
        
        print(f"\n📋 所有村居列表（按街道分组）")
        print("=" * 60)
        
        for street, villages in sorted(villages_by_street.items()):
            print(f"\n🏛️  {street} ({len(villages)}个村居):")
            for i, village_info in enumerate(sorted(villages, key=lambda x: x['NAME']), 1):
                neighbor_count = len(analyzer.neighbor_relations.get(village_info['XZQH'], []))
                print(f"   {i:2d}. {village_info['NAME']} (相邻: {neighbor_count})")
    else:
        # 简单列表显示
        print(f"\n📋 所有村居列表")
        print("=" * 60)
        
        villages = list(analyzer.villages.values())
        villages.sort(key=lambda x: x['info']['NAME'])
        
        for i, village_data in enumerate(villages, 1):
            village_info = village_data['info']
            neighbor_count = len(analyzer.neighbor_relations.get(village_info['XZQH'], []))
            print(f"{i:3d}. {village_info['NAME']:<20} {village_info['PNAME']:<15} (相邻: {neighbor_count})")


def interactive_mode(analyzer: VillageNeighborAnalyzer):
    """交互模式"""
    
    print("\n" + "="*60)
    print("🏘️  村居相邻关系查询工具 - 交互模式")
    print("="*60)
    print("💡 输入村居名称查询相邻关系，输入 'quit' 或 'q' 退出")
    print("💡 特殊命令:")
    print("   'stats' - 显示统计信息")
    print("   'list' - 显示所有村居")
    print("   'list street' - 按街道显示所有村居")
    
    while True:
        try:
            user_input = input("\n📍 请输入村居名称或命令: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 退出程序")
                break
            
            if not user_input:
                print("⚠️  请输入有效的村居名称或命令")
                continue
            
            if user_input.lower() == 'stats':
                show_statistics(analyzer)
                continue
            
            if user_input.lower() == 'list':
                list_all_villages(analyzer, by_street=False)
                continue
            
            if user_input.lower() == 'list street':
                list_all_villages(analyzer, by_street=True)
                continue
            
            # 查询村居相邻关系
            analyzer.print_neighbor_analysis(user_input)
            
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")


def main():
    """主函数"""
    
    parser = argparse.ArgumentParser(
        description='村居相邻关系查询工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python neighbor_query.py "平中社区"
  python neighbor_query.py --batch "平中社区" "东恒社区" "飞鹅新村社区"
  python neighbor_query.py --stats
  python neighbor_query.py --list
  python neighbor_query.py --interactive
  python neighbor_query.py --export neighbor_data.json
        """
    )
    
    parser.add_argument(
        'village',
        nargs='?',
        help='要查询的村居名称或ID'
    )
    
    parser.add_argument(
        '-b', '--batch',
        nargs='+',
        help='批量查询多个村居'
    )
    
    parser.add_argument(
        '-s', '--stats',
        action='store_true',
        help='显示相邻关系统计信息'
    )
    
    parser.add_argument(
        '-l', '--list',
        action='store_true',
        help='列出所有村居'
    )
    
    parser.add_argument(
        '--list-by-street',
        action='store_true',
        help='按街道分组列出所有村居'
    )
    
    parser.add_argument(
        '-i', '--interactive',
        action='store_true',
        help='启动交互模式'
    )
    
    parser.add_argument(
        '-e', '--export',
        metavar='FILE',
        help='导出相邻关系数据到JSON文件'
    )
    
    parser.add_argument(
        '--no-cache',
        action='store_true',
        help='不使用缓存，重新计算相邻关系'
    )
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = VillageNeighborAnalyzer()
    
    # 加载数据
    print("正在加载村居数据...")
    if not analyzer.load_cunju_data():
        print("❌ 加载村居数据失败")
        sys.exit(1)
    
    # 分析相邻关系
    print("正在分析相邻关系...")
    use_cache = not args.no_cache
    analyzer.analyze_neighbor_relations(use_cache=use_cache)
    
    # 根据参数执行相应功能
    if args.interactive:
        interactive_mode(analyzer)
        return
    
    if args.stats:
        show_statistics(analyzer)
        return
    
    if args.list:
        list_all_villages(analyzer, by_street=False)
        return
    
    if args.list_by_street:
        list_all_villages(analyzer, by_street=True)
        return
    
    if args.export:
        analyzer.export_neighbor_relations(args.export)
        return
    
    if args.batch:
        batch_query_neighbors(analyzer, args.batch)
        return
    
    if args.village:
        query_neighbors(analyzer, args.village)
        return
    
    # 没有提供参数，显示帮助
    parser.print_help()


if __name__ == "__main__":
    main()
