#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
村居分级防控管理系统演示
展示系统的完整功能流程
"""

from epidemic_control_system import (
    EpidemicControlSystem, VillageLevel, RiskLevel
)
import datetime


def demo_epidemic_control_system():
    """演示疫情防控管理系统"""
    
    print("🦠 村居分级防控管理系统演示")
    print("=" * 60)
    
    # 1. 初始化系统
    print("\n📋 步骤1: 初始化系统")
    print("-" * 30)
    system = EpidemicControlSystem()
    
    # 显示初始状态
    stats = system.get_village_statistics()
    print(f"✅ 系统初始化完成")
    print(f"   总村居数: {stats['total_villages']}")
    print(f"   初始分级: 全部为三类村居")
    
    # 2. 添加疫点
    print("\n📋 步骤2: 添加疫点")
    print("-" * 30)
    
    # 添加东平北路疫点
    success = system.add_epidemic_point(
        point_id="EP001",
        name="东平北路疫点",
        address="广州市白云区东平北路11号",
        coordinates=(42572.58, 243235.79),
        is_local=True
    )
    
    if success:
        stats = system.get_village_statistics()
        print(f"✅ 疫点添加成功，村居分级已更新:")
        print(f"   一类村居: {stats['level_1']} 个 (300米警戒区内)")
        print(f"   二类村居: {stats['level_2']} 个 (与一类村居相邻)")
        print(f"   三类村居: {stats['level_3']} 个 (其他村居)")
    
    # 3. 查看一类村居详情
    print("\n📋 步骤3: 查看一类村居详情")
    print("-" * 30)
    
    level_1_villages = system.get_villages_by_level(VillageLevel.LEVEL_1)
    
    for i, village in enumerate(level_1_villages, 1):
        print(f"{i}. {village['name']} ({village['street']})")
        measures = village['control_measures']
        print(f"   监测频率: {measures['monitoring_frequency']}")
        print(f"   消杀频率: {measures['disinfection_frequency']}")
        print(f"   特殊要求: {len(measures['special_requirements'])} 项")
    
    # 4. 查看二类村居详情
    print("\n📋 步骤4: 查看二类村居详情")
    print("-" * 30)
    
    level_2_villages = system.get_villages_by_level(VillageLevel.LEVEL_2)
    
    for i, village in enumerate(level_2_villages[:3], 1):  # 只显示前3个
        print(f"{i}. {village['name']} ({village['street']})")
        measures = village['control_measures']
        print(f"   监测频率: {measures['monitoring_frequency']}")
        print(f"   消杀频率: {measures['disinfection_frequency']}")
    
    if len(level_2_villages) > 3:
        print(f"   ... 还有 {len(level_2_villages) - 3} 个二类村居")
    
    # 5. 模拟风险监测更新
    print("\n📋 步骤5: 模拟风险监测更新")
    print("-" * 30)
    
    if level_1_villages:
        # 选择第一个一类村居进行风险更新
        target_village = level_1_villages[0]
        village_id = None
        
        # 找到村居ID
        for vid, status in system.village_status.items():
            if status.village_name == target_village['name']:
                village_id = vid
                break
        
        if village_id:
            # 模拟高风险监测结果
            monitoring_data = {
                'monitoring_date': datetime.date.today().isoformat(),
                'mosquito_density': 'high',
                'breeding_sites': 15,
                'adult_mosquitoes': 8,
                'risk_factors': ['积水', '垃圾堆积', '植被茂密']
            }
            
            system.update_village_risk(
                village_id=village_id,
                risk_level=RiskLevel.HIGH,
                monitoring_data=monitoring_data,
                notes="监测发现蚊媒密度较高，存在传播风险"
            )
            
            print(f"✅ 已更新 {target_village['name']} 的风险等级为高风险")
            
            # 显示更新后的防控措施
            measures = system.get_control_measures(village_id)
            print(f"   更新后消杀频率: {measures['disinfection_frequency']}")
    
    # 6. 模拟连续风险监测（触发升级）
    print("\n📋 步骤6: 模拟连续高风险监测")
    print("-" * 30)
    
    if level_2_villages and village_id:
        # 选择一个二类村居进行连续高风险监测
        target_village_2 = level_2_villages[0]
        village_id_2 = None
        
        for vid, status in system.village_status.items():
            if status.village_name == target_village_2['name']:
                village_id_2 = vid
                break
        
        if village_id_2:
            # 模拟连续2次高风险
            for day in range(2):
                monitoring_data = {
                    'monitoring_date': (datetime.date.today() - datetime.timedelta(days=1-day)).isoformat(),
                    'mosquito_density': 'high',
                    'breeding_sites': 12 + day * 2,
                    'adult_mosquitoes': 6 + day,
                    'risk_factors': ['积水增多', '温度适宜']
                }
                
                system.update_village_risk(
                    village_id=village_id_2,
                    risk_level=RiskLevel.HIGH,
                    monitoring_data=monitoring_data,
                    notes=f"第{day+1}次高风险监测"
                )
            
            status = system.village_status[village_id_2]
            print(f"✅ {target_village_2['name']} 连续2次高风险监测")
            print(f"   当前分级: {status.current_level.value}")
            print(f"   是否需要升级: {'是' if status.should_upgrade() else '否'}")
    
    # 7. 显示高风险村居汇总
    print("\n📋 步骤7: 高风险村居汇总")
    print("-" * 30)
    
    high_risk_villages = system.get_high_risk_villages()
    
    if high_risk_villages:
        print(f"🚨 发现 {len(high_risk_villages)} 个高风险村居:")
        
        for village in high_risk_villages:
            upgrade_status = "⬆️ 建议升级" if village['should_upgrade'] else ""
            print(f"   • {village['name']} ({village['current_level']}) {upgrade_status}")
            print(f"     近期风险趋势: {' → '.join(village['recent_risks'])}")
    else:
        print("✅ 当前没有高风险村居")
    
    # 8. 导出防控数据
    print("\n📋 步骤8: 导出防控数据")
    print("-" * 30)
    
    export_file = system.export_status_data("demo_epidemic_control.json")
    
    if export_file:
        print(f"✅ 防控数据已导出到: {export_file}")
        
        # 显示最终统计
        final_stats = system.get_village_statistics()
        print(f"\n📊 最终防控统计:")
        print(f"   活跃疫点: {final_stats['active_epidemic_points']} 个")
        print(f"   一类村居: {final_stats['level_1']} 个")
        print(f"   二类村居: {final_stats['level_2']} 个")
        print(f"   三类村居: {final_stats['level_3']} 个")
        print(f"   高风险村居: {final_stats['risk_distribution']['high']} 个")
    
    # 9. 演示防控措施查询
    print("\n📋 步骤9: 防控措施查询示例")
    print("-" * 30)
    
    if level_1_villages:
        example_village = level_1_villages[0]
        village_id = None
        
        for vid, status in system.village_status.items():
            if status.village_name == example_village['name']:
                village_id = vid
                break
        
        if village_id:
            measures = system.get_control_measures(village_id)
            
            print(f"📋 {example_village['name']} 防控措施详情:")
            print(f"   当前分级: {measures['current_level']}")
            print(f"   当前风险: {measures['current_risk']}")
            print(f"   监测频率: {measures['monitoring_frequency']}")
            print(f"   消杀频率: {measures['disinfection_frequency']}")
            print(f"   防控周期: {measures['control_duration']}")
            
            if measures['special_requirements']:
                print(f"   特殊要求:")
                for req in measures['special_requirements']:
                    print(f"     • {req}")
    
    print(f"\n🎉 演示完成！")
    print(f"系统已成功演示了疫点管理、村居分级、风险评估、")
    print(f"分级调整、防控措施制定等完整功能流程。")


def main():
    """主函数"""
    try:
        demo_epidemic_control_system()
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
