#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地理位置覆盖范围计算器
通过地址查询坐标，计算300米范围内覆盖的村居
"""

import json
import requests
import xml.etree.ElementTree as ET
from typing import List, Dict, Tuple, Optional
from shapely.geometry import Point, Polygon
from shapely.ops import transform
import urllib.parse


class GeoCoverageCalculator:
    """地理覆盖范围计算器"""
    
    def __init__(self, cunju_file_path: str = "cunju.json"):
        """
        初始化计算器
        
        Args:
            cunju_file_path: 村居数据文件路径
        """
        self.cunju_file_path = cunju_file_path
        self.cunju_data = None
        self.api_url = "https://ypt.gzlpc.gov.cn/ApiService/5537/5642b26381d7c8f0b876b0e8f74288e8"
        
    def load_cunju_data(self) -> bool:
        """
        加载村居数据
        
        Returns:
            bool: 加载是否成功
        """
        try:
            with open(self.cunju_file_path, 'r', encoding='utf-8') as f:
                self.cunju_data = json.load(f)
            print(f"成功加载村居数据，共 {len(self.cunju_data['recordsets'][0]['features'])} 个村居")
            return True
        except Exception as e:
            print(f"加载村居数据失败: {e}")
            return False
    
    def query_coordinates_by_address(self, address: str) -> Optional[Tuple[float, float]]:
        """
        通过地址查询GZ2000坐标
        
        Args:
            address: 地址字符串
            
        Returns:
            Tuple[float, float]: (x, y) GZ2000坐标，失败返回None
        """
        try:
            # URL编码地址
            encoded_address = urllib.parse.quote(address)
            
            # 构建请求URL
            params = {
                'request': '1',
                'q': address,
                's': '*'
            }
            
            # 发送请求
            response = requests.get(self.api_url, params=params, timeout=10)
            response.raise_for_status()
            
            # 解析XML响应
            root = ET.fromstring(response.text)
            
            # 检查是否有结果
            record_count = int(root.find('RecordCount').text)
            if record_count == 0:
                print(f"未找到地址 '{address}' 的坐标信息")
                return None
            
            # 提取坐标
            row = root.find('Row')
            x0 = float(row.find('X0').text)
            y0 = float(row.find('Y0').text)
            
            # 提取匹配信息
            match_value = float(row.find('MatchValue').text)
            actual_address = row.find('Address').text
            
            print(f"查询地址: {address}")
            print(f"匹配地址: {actual_address}")
            print(f"匹配度: {match_value:.2f}")
            print(f"GZ2000坐标: ({x0:.2f}, {y0:.2f})")
            
            return (x0, y0)
            
        except requests.RequestException as e:
            print(f"API请求失败: {e}")
            return None
        except ET.ParseError as e:
            print(f"XML解析失败: {e}")
            return None
        except Exception as e:
            print(f"坐标查询失败: {e}")
            return None
    
    def create_polygon_from_points(self, points: List[Dict]) -> Polygon:
        """
        从点列表创建多边形
        
        Args:
            points: 点列表，每个点包含x和y坐标
            
        Returns:
            Polygon: Shapely多边形对象
        """
        coords = [(point['x'], point['y']) for point in points]
        return Polygon(coords)
    
    def calculate_coverage(self, center_point: Tuple[float, float], radius: float = 300, sample_points: int = 360) -> List[Dict]:
        """
        计算指定点位周围指定半径范围内覆盖的村居（使用圆周采样法）

        Args:
            center_point: 中心点坐标 (x, y)
            radius: 覆盖半径，单位米，默认300米
            sample_points: 圆周采样点数，默认360个（每度一个点）

        Returns:
            List[Dict]: 覆盖的村居信息列表
        """
        if not self.cunju_data:
            print("请先加载村居数据")
            return []

        import math

        # 生成圆周上的采样点
        sampling_points = []
        for i in range(sample_points):
            angle = 2 * math.pi * i / sample_points  # 角度（弧度）
            x = center_point[0] + radius * math.cos(angle)
            y = center_point[1] + radius * math.sin(angle)
            sampling_points.append(Point(x, y))

        print(f"在{radius}米圆周上生成了{len(sampling_points)}个采样点")

        # 创建村居多边形字典，避免重复创建
        village_polygons = {}
        village_info_dict = {}

        features = self.cunju_data['recordsets'][0]['features']

        for feature in features:
            try:
                # 提取村居信息
                field_names = feature['fieldNames']
                field_values = feature['fieldValues']
                village_info = dict(zip(field_names, field_values))

                # 提取几何信息
                geometry = feature['geometry']
                points = geometry['points']

                # 创建村居多边形
                village_polygon = self.create_polygon_from_points(points)

                village_id = village_info['XZQH']  # 使用行政区划代码作为唯一标识
                village_polygons[village_id] = village_polygon
                village_info_dict[village_id] = {
                    'info': village_info,
                    'geometry': geometry,
                    'polygon': village_polygon
                }

            except Exception as e:
                print(f"处理村居数据时出错: {e}")
                continue

        # 统计每个村居被采样到的点数
        village_hit_counts = {}

        for point in sampling_points:
            for village_id, polygon in village_polygons.items():
                try:
                    if polygon.contains(point):
                        if village_id not in village_hit_counts:
                            village_hit_counts[village_id] = 0
                        village_hit_counts[village_id] += 1
                        break  # 一个点只能属于一个村居，找到后跳出
                except Exception as e:
                    continue

        # 构建结果列表
        covered_villages = []
        center = Point(center_point[0], center_point[1])

        for village_id, hit_count in village_hit_counts.items():
            village_data = village_info_dict[village_id]
            village_info = village_data['info']
            geometry = village_data['geometry']
            village_polygon = village_data['polygon']

            # 计算覆盖比例（被采样到的点数占总采样点数的比例）
            coverage_ratio = hit_count / sample_points

            # 计算中心点到村居中心的距离
            village_center = Point(geometry['center']['x'], geometry['center']['y'])
            distance_to_center = center.distance(village_center)

            # 计算村居面积
            village_area = village_polygon.area

            # 估算覆盖面积（基于采样比例）
            estimated_coverage_area = village_area * (hit_count / sample_points) if sample_points > 0 else 0

            covered_info = {
                'village_info': village_info,
                'hit_count': hit_count,
                'sample_points': sample_points,
                'coverage_ratio': coverage_ratio,
                'distance_to_center': distance_to_center,
                'village_center': (geometry['center']['x'], geometry['center']['y']),
                'village_area': village_area,
                'estimated_coverage_area': estimated_coverage_area
            }

            covered_villages.append(covered_info)

        # 按覆盖比例降序排序，覆盖比例相同时按距离升序排序
        covered_villages.sort(key=lambda x: (-x['coverage_ratio'], x['distance_to_center']))

        return covered_villages
    
    def print_coverage_result(self, covered_villages: List[Dict], center_point: Tuple[float, float], radius: float = 300):
        """
        打印覆盖结果（圆周采样法）

        Args:
            covered_villages: 覆盖的村居列表
            center_point: 中心点坐标
            radius: 覆盖半径
        """
        print(f"\n=== 覆盖范围分析结果（圆周采样法）===")
        print(f"中心点坐标: ({center_point[0]:.2f}, {center_point[1]:.2f})")
        print(f"覆盖半径: {radius}米")
        print(f"覆盖村居数量: {len(covered_villages)}")

        if covered_villages:
            total_sample_points = covered_villages[0]['sample_points']
            print(f"采样点数: {total_sample_points}个（圆周均匀分布）")
        print()

        if not covered_villages:
            print("未找到覆盖的村居")
            return

        print("覆盖的村居详情:")
        print("-" * 90)

        for i, village in enumerate(covered_villages, 1):
            info = village['village_info']
            hit_count = village['hit_count']
            sample_points = village['sample_points']

            print(f"{i}. {info['NAME']}")
            print(f"   所属街道: {info['PNAME']}")
            print(f"   所属区域: {info['PPNAME']}")
            print(f"   行政区划: {info['XZQH']}")
            print(f"   到中心距离: {village['distance_to_center']:.2f}米")
            print(f"   采样命中: {hit_count}/{sample_points}个点 ({village['coverage_ratio']:.2%})")
            print(f"   估算覆盖面积: {village['estimated_coverage_area']:.2f}平方米")
            print(f"   村居总面积: {village['village_area']:.2f}平方米")
            print(f"   村居中心: ({village['village_center'][0]:.2f}, {village['village_center'][1]:.2f})")
            print()
    
    def calculate_coverage_by_address(self, address: str, radius: float = 300) -> List[Dict]:
        """
        通过地址计算覆盖范围
        
        Args:
            address: 地址字符串
            radius: 覆盖半径，单位米，默认300米
            
        Returns:
            List[Dict]: 覆盖的村居信息列表
        """
        # 查询坐标
        coordinates = self.query_coordinates_by_address(address)
        if not coordinates:
            return []
        
        # 计算覆盖范围
        covered_villages = self.calculate_coverage(coordinates, radius)
        
        # 打印结果
        self.print_coverage_result(covered_villages, coordinates, radius)
        
        return covered_villages


def main():
    """主函数示例"""
    # 创建计算器实例
    calculator = GeoCoverageCalculator()
    
    # 加载村居数据
    if not calculator.load_cunju_data():
        return
    
    # 示例：查询指定地址的覆盖范围
    test_address = "东平北路 11 号"
    print(f"正在查询地址 '{test_address}' 的300米覆盖范围...")
    
    covered_villages = calculator.calculate_coverage_by_address(test_address)
    
    # 可以尝试其他地址
    # other_addresses = [
    #     "白云区三元里大道",
    #     "广州市白云区机场路",
    # ]
    # 
    # for addr in other_addresses:
    #     print(f"\n{'='*50}")
    #     calculator.calculate_coverage_by_address(addr)


if __name__ == "__main__":
    main()
