#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实用坐标转换工具
基于已知数据点进行GZ2000与WGS84之间的转换
"""

import json
import math
import argparse
from typing import Tuple, List, Dict


class PracticalCoordinateConverter:
    """实用坐标转换器 - 基于已知控制点"""
    
    def __init__(self):
        """初始化转换器"""
        # 已知的控制点数据 (从API获得的准确对应关系)
        self.control_points = [
            {
                'name': '东平北路11号',
                'gz2000': (42572.577598, 243235.793091),
                'wgs84': (113.30864852, 23.25569948)
            }
            # 可以添加更多控制点来提高精度
        ]
        
        # 计算转换参数
        self._calculate_transform_parameters()
    
    def _calculate_transform_parameters(self):
        """计算转换参数"""
        
        if len(self.control_points) == 1:
            # 单点校准 - 使用线性近似
            cp = self.control_points[0]
            self.ref_gz2000 = cp['gz2000']
            self.ref_wgs84 = cp['wgs84']
            
            # 在广州地区的近似比例因子
            # 1度经度 ≈ 96000米，1度纬度 ≈ 111000米
            self.lon_scale = 96000.0  # 米/度
            self.lat_scale = 111000.0  # 米/度
            
            print(f"✅ 使用单点校准转换 (参考点: {cp['name']})")
            
        else:
            # 多点校准 - 可以实现更复杂的转换
            print(f"✅ 使用多点校准转换 ({len(self.control_points)}个控制点)")
            # TODO: 实现多点校准算法
    
    def gz2000_to_wgs84(self, x: float, y: float) -> Tuple[float, float]:
        """
        GZ2000转WGS84
        
        Args:
            x: GZ2000 X坐标 (米)
            y: GZ2000 Y坐标 (米)
            
        Returns:
            Tuple[float, float]: (经度, 纬度) WGS84坐标
        """
        # 计算相对于参考点的偏移
        dx = x - self.ref_gz2000[0]
        dy = y - self.ref_gz2000[1]
        
        # 转换为经纬度偏移
        dlon = dx / self.lon_scale
        dlat = dy / self.lat_scale
        
        # 计算目标坐标
        lon = self.ref_wgs84[0] + dlon
        lat = self.ref_wgs84[1] + dlat
        
        return (lon, lat)
    
    def wgs84_to_gz2000(self, lon: float, lat: float) -> Tuple[float, float]:
        """
        WGS84转GZ2000
        
        Args:
            lon: WGS84经度
            lat: WGS84纬度
            
        Returns:
            Tuple[float, float]: (X, Y) GZ2000坐标
        """
        # 计算经纬度偏移
        dlon = lon - self.ref_wgs84[0]
        dlat = lat - self.ref_wgs84[1]
        
        # 转换为米偏移
        dx = dlon * self.lon_scale
        dy = dlat * self.lat_scale
        
        # 计算目标坐标
        x = self.ref_gz2000[0] + dx
        y = self.ref_gz2000[1] + dy
        
        return (x, y)
    
    def add_control_point(self, name: str, gz2000: Tuple[float, float], wgs84: Tuple[float, float]):
        """添加控制点"""
        self.control_points.append({
            'name': name,
            'gz2000': gz2000,
            'wgs84': wgs84
        })
        self._calculate_transform_parameters()
    
    def get_conversion_accuracy_estimate(self, x: float, y: float) -> Dict:
        """估算转换精度"""
        
        # 计算到参考点的距离
        ref_x, ref_y = self.ref_gz2000
        distance = math.sqrt((x - ref_x)**2 + (y - ref_y)**2)
        
        # 估算误差 (距离参考点越远，误差越大)
        base_error = 10.0  # 基础误差 (米)
        distance_factor = distance / 10000.0  # 每10km增加的误差因子
        estimated_error = base_error + distance_factor * 50.0
        
        return {
            'distance_to_reference': distance,
            'estimated_error_meters': estimated_error,
            'accuracy_level': 'high' if distance < 5000 else 'medium' if distance < 15000 else 'low'
        }


def test_practical_conversion():
    """测试实用转换"""
    
    print("🧪 实用坐标转换测试")
    print("=" * 40)
    
    converter = PracticalCoordinateConverter()
    
    # 测试数据
    test_cases = [
        {
            'name': '东平北路11号 (参考点)',
            'gz2000': (42572.577598, 243235.793091),
            'wgs84': (113.30864852, 23.25569948)
        },
        {
            'name': '平中社区中心',
            'gz2000': (42883.53, 243564.83),
            'wgs84': None  # 待转换
        },
        {
            'name': '东恒社区中心',
            'gz2000': (42213.70, 242735.30),
            'wgs84': None  # 待转换
        }
    ]
    
    print("📍 转换测试结果:")
    print("-" * 60)
    
    for i, case in enumerate(test_cases, 1):
        gz2000 = case['gz2000']
        known_wgs84 = case['wgs84']
        
        # 转换为WGS84
        converted_wgs84 = converter.gz2000_to_wgs84(gz2000[0], gz2000[1])
        
        # 获取精度估算
        accuracy = converter.get_conversion_accuracy_estimate(gz2000[0], gz2000[1])
        
        print(f"{i}. {case['name']}")
        print(f"   GZ2000: ({gz2000[0]:.2f}, {gz2000[1]:.2f})")
        print(f"   转换WGS84: ({converted_wgs84[0]:.6f}, {converted_wgs84[1]:.6f})")
        
        if known_wgs84:
            # 计算误差
            lon_error = abs(converted_wgs84[0] - known_wgs84[0])
            lat_error = abs(converted_wgs84[1] - known_wgs84[1])
            
            print(f"   已知WGS84: ({known_wgs84[0]:.6f}, {known_wgs84[1]:.6f})")
            print(f"   经度误差: {lon_error:.6f}度 ({lon_error * 96000:.1f}米)")
            print(f"   纬度误差: {lat_error:.6f}度 ({lat_error * 111000:.1f}米)")
        
        print(f"   距参考点: {accuracy['distance_to_reference']:.0f}米")
        print(f"   估算误差: ±{accuracy['estimated_error_meters']:.0f}米")
        print(f"   精度等级: {accuracy['accuracy_level']}")
        print()


def convert_all_villages():
    """转换所有村居坐标"""
    
    print("🏘️  转换所有村居坐标为WGS84")
    print("=" * 40)
    
    converter = PracticalCoordinateConverter()
    
    # 加载村居数据
    try:
        with open('cunju.json', 'r', encoding='utf-8') as f:
            cunju_data = json.load(f)
    except Exception as e:
        print(f"❌ 加载村居数据失败: {e}")
        return
    
    features = cunju_data['recordsets'][0]['features']
    converted_data = {}
    
    print(f"🔄 正在转换 {len(features)} 个村居...")
    
    for feature in features:
        field_names = feature['fieldNames']
        field_values = feature['fieldValues']
        village_info = dict(zip(field_names, field_values))
        
        center = feature['geometry']['center']
        gz2000_coords = (center['x'], center['y'])
        
        # 转换为WGS84
        wgs84_coords = converter.gz2000_to_wgs84(gz2000_coords[0], gz2000_coords[1])
        
        # 获取精度估算
        accuracy = converter.get_conversion_accuracy_estimate(gz2000_coords[0], gz2000_coords[1])
        
        converted_data[village_info['XZQH']] = {
            'name': village_info['NAME'],
            'street': village_info['PNAME'],
            'district': village_info['PPNAME'],
            'coordinates': {
                'gz2000': {
                    'x': gz2000_coords[0],
                    'y': gz2000_coords[1]
                },
                'wgs84': {
                    'longitude': wgs84_coords[0],
                    'latitude': wgs84_coords[1]
                }
            },
            'conversion_accuracy': {
                'distance_to_reference_m': accuracy['distance_to_reference'],
                'estimated_error_m': accuracy['estimated_error_meters'],
                'accuracy_level': accuracy['accuracy_level']
            }
        }
    
    # 保存结果
    output_file = "villages_with_wgs84.json"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(converted_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 转换完成，保存到: {output_file}")
        
        # 统计精度分布
        accuracy_stats = {'high': 0, 'medium': 0, 'low': 0}
        for data in converted_data.values():
            level = data['conversion_accuracy']['accuracy_level']
            accuracy_stats[level] += 1
        
        print(f"📊 精度分布:")
        print(f"   高精度 (距参考点<5km): {accuracy_stats['high']} 个")
        print(f"   中精度 (距参考点5-15km): {accuracy_stats['medium']} 个")
        print(f"   低精度 (距参考点>15km): {accuracy_stats['low']} 个")
        
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")


def interactive_converter():
    """交互式转换工具"""
    
    print("🛠️  交互式坐标转换工具")
    print("=" * 40)
    
    converter = PracticalCoordinateConverter()
    
    while True:
        print(f"\n选择功能:")
        print("1. GZ2000 -> WGS84")
        print("2. WGS84 -> GZ2000")
        print("3. 查看控制点")
        print("4. 退出")
        
        choice = input("请选择 (1-4): ").strip()
        
        if choice == '1':
            try:
                x = float(input("请输入GZ2000 X坐标 (米): "))
                y = float(input("请输入GZ2000 Y坐标 (米): "))
                
                lon, lat = converter.gz2000_to_wgs84(x, y)
                accuracy = converter.get_conversion_accuracy_estimate(x, y)
                
                print(f"✅ 转换结果:")
                print(f"   WGS84: 经度={lon:.8f}, 纬度={lat:.8f}")
                print(f"   距参考点: {accuracy['distance_to_reference']:.0f}米")
                print(f"   估算误差: ±{accuracy['estimated_error_meters']:.0f}米")
                print(f"   精度等级: {accuracy['accuracy_level']}")
                
            except ValueError:
                print("❌ 输入格式错误")
                
        elif choice == '2':
            try:
                lon = float(input("请输入WGS84经度: "))
                lat = float(input("请输入WGS84纬度: "))
                
                x, y = converter.wgs84_to_gz2000(lon, lat)
                accuracy = converter.get_conversion_accuracy_estimate(x, y)
                
                print(f"✅ 转换结果:")
                print(f"   GZ2000: X={x:.2f}米, Y={y:.2f}米")
                print(f"   距参考点: {accuracy['distance_to_reference']:.0f}米")
                print(f"   估算误差: ±{accuracy['estimated_error_meters']:.0f}米")
                print(f"   精度等级: {accuracy['accuracy_level']}")
                
            except ValueError:
                print("❌ 输入格式错误")
                
        elif choice == '3':
            print(f"\n📍 当前控制点:")
            for i, cp in enumerate(converter.control_points, 1):
                print(f"{i}. {cp['name']}")
                print(f"   GZ2000: {cp['gz2000']}")
                print(f"   WGS84: {cp['wgs84']}")
                
        elif choice == '4':
            print("👋 退出转换工具")
            break
        else:
            print("❌ 无效选择")


def main():
    """主函数"""
    
    parser = argparse.ArgumentParser(description='实用坐标转换工具')
    parser.add_argument('--test', action='store_true', help='运行转换测试')
    parser.add_argument('--convert-all', action='store_true', help='转换所有村居坐标')
    parser.add_argument('--interactive', action='store_true', help='交互式转换')
    
    args = parser.parse_args()
    
    print("🌍 实用坐标转换工具")
    print("基于已知控制点的GZ2000 <-> WGS84转换")
    print("=" * 50)
    
    if args.test:
        test_practical_conversion()
    
    if args.convert_all:
        convert_all_villages()
    
    if args.interactive:
        interactive_converter()
    
    if not any([args.test, args.convert_all, args.interactive]):
        # 默认运行测试
        test_practical_conversion()


if __name__ == "__main__":
    main()
