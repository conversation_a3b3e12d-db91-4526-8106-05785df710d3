#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标转换工具
实现GZ2000与WGS84坐标系之间的相互转换
"""

import math
import json
from typing import Tuple, List, Dict, Optional


class CoordinateConverter:
    """坐标转换器"""
    
    def __init__(self):
        """初始化转换器"""
        # GZ2000坐标系参数 (基于CGCS2000椭球)
        self.a = 6378137.0  # 长半轴 (米)
        self.f = 1/298.257222101  # 扁率
        self.e2 = 2 * self.f - self.f * self.f  # 第一偏心率平方
        
        # GZ2000投影参数 (假设参数，实际应用中需要准确参数)
        self.central_meridian = math.radians(113.0)  # 中央子午线 (广州附近)
        self.false_easting = 500000.0  # 东偏移 (米)
        self.false_northing = 0.0  # 北偏移 (米)
        self.scale_factor = 1.0  # 比例因子
        
        print("⚠️  注意：这是基于估算参数的转换，实际应用需要准确的投影参数！")
    
    def gz2000_to_wgs84_approximate(self, x: float, y: float) -> Tuple[float, float]:
        """
        GZ2000转WGS84 (近似转换)
        
        Args:
            x: GZ2000 X坐标 (米)
            y: GZ2000 Y坐标 (米)
            
        Returns:
            Tuple[float, float]: (经度, 纬度) WGS84坐标
        """
        # 这是一个简化的近似转换
        # 实际应用中需要使用专业的投影转换库
        
        # 简单的线性近似 (仅用于演示)
        # 基于广州地区的经验参数
        
        # 将GZ2000坐标转换为相对于参考点的偏移
        ref_gz2000_x = 42000.0  # 参考点GZ2000 X
        ref_gz2000_y = 243000.0  # 参考点GZ2000 Y
        ref_wgs84_lon = 113.3  # 参考点WGS84经度
        ref_wgs84_lat = 23.25  # 参考点WGS84纬度
        
        # 计算偏移量
        dx = x - ref_gz2000_x
        dy = y - ref_gz2000_y
        
        # 转换为经纬度偏移 (近似)
        # 在广州地区，1度经度约等于96000米，1度纬度约等于111000米
        dlon = dx / 96000.0
        dlat = dy / 111000.0
        
        lon = ref_wgs84_lon + dlon
        lat = ref_wgs84_lat + dlat
        
        return (lon, lat)
    
    def wgs84_to_gz2000_approximate(self, lon: float, lat: float) -> Tuple[float, float]:
        """
        WGS84转GZ2000 (近似转换)
        
        Args:
            lon: WGS84经度
            lat: WGS84纬度
            
        Returns:
            Tuple[float, float]: (X, Y) GZ2000坐标
        """
        # 参考点
        ref_gz2000_x = 42000.0
        ref_gz2000_y = 243000.0
        ref_wgs84_lon = 113.3
        ref_wgs84_lat = 23.25
        
        # 计算经纬度偏移
        dlon = lon - ref_wgs84_lon
        dlat = lat - ref_wgs84_lat
        
        # 转换为米偏移
        dx = dlon * 96000.0
        dy = dlat * 111000.0
        
        x = ref_gz2000_x + dx
        y = ref_gz2000_y + dy
        
        return (x, y)


def try_pyproj_conversion():
    """尝试使用pyproj进行精确转换"""
    
    try:
        from pyproj import Transformer
        
        print("✅ 检测到pyproj库，可以进行精确坐标转换")
        
        # 创建转换器
        # 注意：这里使用的EPSG代码可能需要根据实际情况调整
        try:
            # 尝试从CGCS2000地理坐标系转换
            transformer_to_wgs84 = Transformer.from_crs("EPSG:4490", "EPSG:4326")
            transformer_from_wgs84 = Transformer.from_crs("EPSG:4326", "EPSG:4490")
            
            print("🔧 创建了CGCS2000 <-> WGS84转换器")
            return transformer_to_wgs84, transformer_from_wgs84
            
        except Exception as e:
            print(f"⚠️  创建转换器失败: {e}")
            return None, None
            
    except ImportError:
        print("❌ 未安装pyproj库，使用近似转换方法")
        print("💡 安装方法: pip install pyproj")
        return None, None


def test_coordinate_conversion():
    """测试坐标转换"""
    
    print("\n🧪 坐标转换测试")
    print("=" * 40)
    
    converter = CoordinateConverter()
    
    # 测试数据：东平北路11号
    test_gz2000 = (42572.577598, 243235.793091)
    known_wgs84 = (113.30864852, 23.25569948)
    
    print(f"📍 测试数据:")
    print(f"   已知GZ2000: {test_gz2000}")
    print(f"   已知WGS84:  {known_wgs84}")
    
    # 近似转换测试
    print(f"\n🔄 近似转换测试:")
    converted_wgs84 = converter.gz2000_to_wgs84_approximate(test_gz2000[0], test_gz2000[1])
    print(f"   转换结果:   {converted_wgs84}")
    
    # 计算误差
    lon_error = abs(converted_wgs84[0] - known_wgs84[0])
    lat_error = abs(converted_wgs84[1] - known_wgs84[1])
    
    print(f"   经度误差:   {lon_error:.6f}度 ({lon_error * 96000:.1f}米)")
    print(f"   纬度误差:   {lat_error:.6f}度 ({lat_error * 111000:.1f}米)")
    
    # 反向转换测试
    print(f"\n🔄 反向转换测试:")
    converted_gz2000 = converter.wgs84_to_gz2000_approximate(known_wgs84[0], known_wgs84[1])
    print(f"   转换结果:   {converted_gz2000}")
    
    x_error = abs(converted_gz2000[0] - test_gz2000[0])
    y_error = abs(converted_gz2000[1] - test_gz2000[1])
    
    print(f"   X坐标误差:  {x_error:.1f}米")
    print(f"   Y坐标误差:  {y_error:.1f}米")
    
    # 尝试pyproj转换
    print(f"\n🔧 尝试精确转换:")
    transformer_to_wgs84, transformer_from_wgs84 = try_pyproj_conversion()
    
    if transformer_to_wgs84:
        print("✅ 可以使用pyproj进行精确转换")
        print("💡 但需要确定GZ2000的准确EPSG代码或投影参数")
    else:
        print("❌ 无法进行精确转换，建议:")
        print("   1. 安装pyproj库")
        print("   2. 获取GZ2000的准确投影参数")
        print("   3. 或使用专业GIS软件进行转换")


def convert_village_coordinates():
    """转换村居坐标为WGS84"""
    
    print(f"\n🏘️  村居坐标转换示例")
    print("=" * 40)
    
    converter = CoordinateConverter()
    
    # 加载村居数据
    try:
        with open('cunju.json', 'r', encoding='utf-8') as f:
            cunju_data = json.load(f)
    except Exception as e:
        print(f"❌ 加载村居数据失败: {e}")
        return
    
    features = cunju_data['recordsets'][0]['features']
    
    print(f"📊 转换前5个村居的坐标:")
    print("-" * 60)
    
    for i, feature in enumerate(features[:5]):
        field_names = feature['fieldNames']
        field_values = feature['fieldValues']
        village_info = dict(zip(field_names, field_values))
        
        center = feature['geometry']['center']
        gz2000_coords = (center['x'], center['y'])
        
        # 转换为WGS84
        wgs84_coords = converter.gz2000_to_wgs84_approximate(gz2000_coords[0], gz2000_coords[1])
        
        print(f"{i+1}. {village_info['NAME']}")
        print(f"   GZ2000: ({gz2000_coords[0]:.2f}, {gz2000_coords[1]:.2f})")
        print(f"   WGS84:  ({wgs84_coords[0]:.6f}, {wgs84_coords[1]:.6f})")
        print()


def create_conversion_tool():
    """创建转换工具"""
    
    print(f"\n🛠️  坐标转换工具")
    print("=" * 30)
    
    converter = CoordinateConverter()
    
    while True:
        print(f"\n选择转换方向:")
        print("1. GZ2000 -> WGS84")
        print("2. WGS84 -> GZ2000")
        print("3. 退出")
        
        choice = input("请选择 (1-3): ").strip()
        
        if choice == '1':
            try:
                x = float(input("请输入GZ2000 X坐标 (米): "))
                y = float(input("请输入GZ2000 Y坐标 (米): "))
                
                lon, lat = converter.gz2000_to_wgs84_approximate(x, y)
                print(f"转换结果: 经度={lon:.6f}, 纬度={lat:.6f}")
                
            except ValueError:
                print("❌ 输入格式错误")
                
        elif choice == '2':
            try:
                lon = float(input("请输入WGS84经度: "))
                lat = float(input("请输入WGS84纬度: "))
                
                x, y = converter.wgs84_to_gz2000_approximate(lon, lat)
                print(f"转换结果: X={x:.2f}米, Y={y:.2f}米")
                
            except ValueError:
                print("❌ 输入格式错误")
                
        elif choice == '3':
            print("👋 退出转换工具")
            break
        else:
            print("❌ 无效选择")


def main():
    """主函数"""
    
    print("🌍 GZ2000 <-> WGS84 坐标转换工具")
    print("=" * 50)
    
    test_coordinate_conversion()
    convert_village_coordinates()
    
    # 交互式转换工具
    use_tool = input("\n是否使用交互式转换工具? (y/n): ").strip().lower()
    if use_tool == 'y':
        create_conversion_tool()
    
    print(f"\n📋 重要说明:")
    print("1. 当前使用的是近似转换算法")
    print("2. 误差范围：经纬度约±0.001度，坐标约±100米")
    print("3. 如需精确转换，请:")
    print("   - 安装pyproj库: pip install pyproj")
    print("   - 获取GZ2000的准确投影参数")
    print("   - 或使用专业GIS软件")


if __name__ == "__main__":
    main()
