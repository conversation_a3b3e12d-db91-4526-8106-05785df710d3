#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
村居查询工具 - 根据地址或坐标查找所属村居
"""

import sys
import argparse
from geo_coverage_calculator import GeoCoverageCalculator


def find_village_by_address(address: str):
    """根据地址查找所属村居"""
    
    calculator = GeoCoverageCalculator()
    
    # 加载村居数据
    print("正在加载村居数据...")
    if not calculator.load_cunju_data():
        print("❌ 加载村居数据失败")
        return None
    
    # 查询坐标
    print(f"\n🔍 正在查询地址: {address}")
    coordinates = calculator.query_coordinates_by_address(address)
    
    if not coordinates:
        print("❌ 无法获取地址坐标")
        return None
    
    # 查找所属村居
    village = calculator.find_village_by_point(coordinates)
    
    if village:
        info = village['village_info']
        geometry = village['geometry']
        
        print(f"\n✅ 找到所属村居:")
        print(f"📍 地址: {address}")
        print(f"📍 坐标: ({coordinates[0]:.2f}, {coordinates[1]:.2f})")
        print("-" * 50)
        print(f"🏘️  村居名称: {info['NAME']}")
        print(f"🏛️  所属街道: {info['PNAME']}")
        print(f"🏙️  所属区域: {info['PPNAME']}")
        print(f"🔢 行政区划: {info['XZQH']}")
        print(f"📐 村居面积: {float(info['SmArea']):.2f}平方米")
        print(f"📏 村居周长: {float(info['SmPerimeter']):.2f}米")
        print(f"🎯 村居中心: ({geometry['center']['x']:.2f}, {geometry['center']['y']:.2f})")
        
        # 计算到村居中心的距离
        center_x, center_y = geometry['center']['x'], geometry['center']['y']
        distance_to_center = ((coordinates[0] - center_x)**2 + (coordinates[1] - center_y)**2)**0.5
        print(f"📏 到村居中心距离: {distance_to_center:.2f}米")
        
        return village
    else:
        print(f"\n❌ 未找到所属村居")
        print(f"📍 地址: {address}")
        print(f"📍 坐标: ({coordinates[0]:.2f}, {coordinates[1]:.2f})")
        print("💡 可能原因:")
        print("   - 地址位于村居边界外")
        print("   - 地址位于未收录的区域")
        print("   - 坐标查询不够精确")
        
        return None


def find_village_by_coordinates(x: float, y: float):
    """根据坐标查找所属村居"""
    
    calculator = GeoCoverageCalculator()
    
    # 加载村居数据
    print("正在加载村居数据...")
    if not calculator.load_cunju_data():
        print("❌ 加载村居数据失败")
        return None
    
    coordinates = (x, y)
    print(f"\n🔍 正在查询坐标: ({x:.2f}, {y:.2f})")
    
    # 查找所属村居
    village = calculator.find_village_by_point(coordinates)
    
    if village:
        info = village['village_info']
        geometry = village['geometry']
        
        print(f"\n✅ 找到所属村居:")
        print(f"📍 坐标: ({x:.2f}, {y:.2f})")
        print("-" * 50)
        print(f"🏘️  村居名称: {info['NAME']}")
        print(f"🏛️  所属街道: {info['PNAME']}")
        print(f"🏙️  所属区域: {info['PPNAME']}")
        print(f"🔢 行政区划: {info['XZQH']}")
        print(f"📐 村居面积: {float(info['SmArea']):.2f}平方米")
        print(f"📏 村居周长: {float(info['SmPerimeter']):.2f}米")
        print(f"🎯 村居中心: ({geometry['center']['x']:.2f}, {geometry['center']['y']:.2f})")
        
        # 计算到村居中心的距离
        center_x, center_y = geometry['center']['x'], geometry['center']['y']
        distance_to_center = ((x - center_x)**2 + (y - center_y)**2)**0.5
        print(f"📏 到村居中心距离: {distance_to_center:.2f}米")
        
        return village
    else:
        print(f"\n❌ 未找到所属村居")
        print(f"📍 坐标: ({x:.2f}, {y:.2f})")
        print("💡 可能原因:")
        print("   - 坐标位于村居边界外")
        print("   - 坐标位于未收录的区域")
        
        return None


def batch_find_villages(addresses: list):
    """批量查找村居"""
    
    calculator = GeoCoverageCalculator()
    
    # 加载村居数据
    print("正在加载村居数据...")
    if not calculator.load_cunju_data():
        print("❌ 加载村居数据失败")
        return
    
    print(f"\n🔍 批量查询 {len(addresses)} 个地址:")
    print("=" * 60)
    
    results = []
    
    for i, address in enumerate(addresses, 1):
        print(f"\n{i}. 查询地址: {address}")
        
        # 查询坐标
        coordinates = calculator.query_coordinates_by_address(address)
        
        if not coordinates:
            print(f"   ❌ 无法获取坐标")
            results.append({'address': address, 'village': None, 'coordinates': None})
            continue
        
        # 查找村居
        village = calculator.find_village_by_point(coordinates)
        
        if village:
            info = village['village_info']
            print(f"   ✅ 所属村居: {info['NAME']} ({info['PNAME']}, {info['PPNAME']})")
            results.append({'address': address, 'village': village, 'coordinates': coordinates})
        else:
            print(f"   ❌ 未找到所属村居")
            results.append({'address': address, 'village': None, 'coordinates': coordinates})
    
    # 汇总结果
    print(f"\n📊 批量查询结果汇总:")
    print("-" * 60)
    
    found_count = sum(1 for r in results if r['village'])
    print(f"总查询数: {len(addresses)}")
    print(f"成功找到: {found_count}")
    print(f"未找到: {len(addresses) - found_count}")
    
    if found_count > 0:
        print(f"\n成功找到的村居:")
        for r in results:
            if r['village']:
                info = r['village']['village_info']
                print(f"  {r['address']} → {info['NAME']} ({info['PNAME']})")


def interactive_mode():
    """交互模式"""
    
    print("\n" + "="*60)
    print("🏘️  村居查询工具 - 交互模式")
    print("="*60)
    print("💡 输入地址查询所属村居，输入 'quit' 或 'q' 退出")
    print("💡 也可以输入坐标格式: x,y (例如: 42572.58,243235.79)")
    
    calculator = GeoCoverageCalculator()
    
    # 加载村居数据
    print("\n正在加载村居数据...")
    if not calculator.load_cunju_data():
        print("❌ 加载村居数据失败")
        return
    
    while True:
        try:
            user_input = input("\n📍 请输入地址或坐标: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 退出程序")
                break
            
            if not user_input:
                print("⚠️  请输入有效地址或坐标")
                continue
            
            # 检查是否是坐标格式
            if ',' in user_input:
                try:
                    parts = user_input.split(',')
                    if len(parts) == 2:
                        x = float(parts[0].strip())
                        y = float(parts[1].strip())
                        find_village_by_coordinates(x, y)
                        continue
                except ValueError:
                    pass
            
            # 作为地址处理
            find_village_by_address(user_input)
            
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")


def main():
    """主函数"""
    
    parser = argparse.ArgumentParser(
        description='根据地址或坐标查找所属村居',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python village_finder.py "东平北路 11 号"
  python village_finder.py --coordinates 42572.58 243235.79
  python village_finder.py --batch "地址1" "地址2" "地址3"
  python village_finder.py --interactive
        """
    )
    
    parser.add_argument(
        'address',
        nargs='?',
        help='要查询的地址'
    )
    
    parser.add_argument(
        '-c', '--coordinates',
        nargs=2,
        type=float,
        metavar=('X', 'Y'),
        help='GZ2000坐标 (x y)'
    )
    
    parser.add_argument(
        '-b', '--batch',
        nargs='+',
        help='批量查询多个地址'
    )
    
    parser.add_argument(
        '-i', '--interactive',
        action='store_true',
        help='启动交互模式'
    )
    
    args = parser.parse_args()
    
    # 交互模式
    if args.interactive:
        interactive_mode()
        return
    
    # 坐标查询
    if args.coordinates:
        find_village_by_coordinates(args.coordinates[0], args.coordinates[1])
        return
    
    # 批量查询
    if args.batch:
        batch_find_villages(args.batch)
        return
    
    # 单个地址查询
    if args.address:
        find_village_by_address(args.address)
        return
    
    # 没有提供参数，显示帮助
    parser.print_help()


if __name__ == "__main__":
    main()
