#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确坐标转换工具
使用pyproj库进行GZ2000与WGS84之间的精确转换
"""

import json
import argparse
from typing import Tuple, List, Dict, Optional

try:
    from pyproj import Transformer, CRS
    PYPROJ_AVAILABLE = True
except ImportError:
    PYPROJ_AVAILABLE = False


class PreciseCoordinateConverter:
    """精确坐标转换器"""
    
    def __init__(self):
        """初始化转换器"""
        if not PYPROJ_AVAILABLE:
            raise ImportError("需要安装pyproj库: pip install pyproj")
        
        # 尝试不同的转换方法
        self.transformer_to_wgs84 = None
        self.transformer_from_wgs84 = None
        
        self._setup_transformers()
    
    def _setup_transformers(self):
        """设置坐标转换器"""
        
        print("🔧 正在设置坐标转换器...")
        
        # 方法1: 尝试使用CGCS2000地理坐标系 (注意：这个方法可能不适用于GZ2000投影坐标)
        try:
            self.transformer_to_wgs84 = Transformer.from_crs("EPSG:4490", "EPSG:4326", always_xy=True)
            self.transformer_from_wgs84 = Transformer.from_crs("EPSG:4326", "EPSG:4490", always_xy=True)
            print("⚠️  尝试使用CGCS2000地理坐标系转换器 (可能不适用于GZ2000)")
            # 这个方法可能不正确，因为GZ2000是投影坐标系，不是地理坐标系
            # return
        except Exception as e:
            print(f"⚠️  CGCS2000转换器创建失败: {e}")
        
        # 方法2: 尝试使用自定义投影参数
        try:
            # GZ2000可能的投影参数 (基于广州地区的横轴墨卡托投影)
            # 注意：这些参数是估算的，实际参数可能不同
            gz2000_proj = "+proj=tmerc +lat_0=0 +lon_0=113 +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs"
            wgs84_proj = "+proj=longlat +datum=WGS84 +no_defs"

            self.transformer_to_wgs84 = Transformer.from_proj(gz2000_proj, wgs84_proj, always_xy=True)
            self.transformer_from_wgs84 = Transformer.from_proj(wgs84_proj, gz2000_proj, always_xy=True)
            print("✅ 使用自定义投影参数转换器")
            return
        except Exception as e:
            print(f"⚠️  自定义投影转换器创建失败: {e}")

        # 方法3: 使用简化的近似转换
        print("⚠️  使用简化的近似转换方法")
        self.use_approximate = True
    
    def gz2000_to_wgs84(self, x: float, y: float) -> Tuple[float, float]:
        """
        GZ2000转WGS84
        
        Args:
            x: GZ2000 X坐标 (米)
            y: GZ2000 Y坐标 (米)
            
        Returns:
            Tuple[float, float]: (经度, 纬度) WGS84坐标
        """
        if not self.transformer_to_wgs84:
            raise Exception("转换器未初始化")
        
        try:
            lon, lat = self.transformer_to_wgs84.transform(x, y)
            return (lon, lat)
        except Exception as e:
            raise Exception(f"坐标转换失败: {e}")
    
    def wgs84_to_gz2000(self, lon: float, lat: float) -> Tuple[float, float]:
        """
        WGS84转GZ2000
        
        Args:
            lon: WGS84经度
            lat: WGS84纬度
            
        Returns:
            Tuple[float, float]: (X, Y) GZ2000坐标
        """
        if not self.transformer_from_wgs84:
            raise Exception("转换器未初始化")
        
        try:
            x, y = self.transformer_from_wgs84.transform(lon, lat)
            return (x, y)
        except Exception as e:
            raise Exception(f"坐标转换失败: {e}")


def test_precise_conversion():
    """测试精确转换"""
    
    print("\n🧪 精确坐标转换测试")
    print("=" * 40)
    
    try:
        converter = PreciseCoordinateConverter()
    except Exception as e:
        print(f"❌ 转换器初始化失败: {e}")
        return
    
    # 测试数据：东平北路11号
    test_gz2000 = (42572.577598, 243235.793091)
    known_wgs84 = (113.30864852, 23.25569948)
    
    print(f"📍 测试数据:")
    print(f"   已知GZ2000: {test_gz2000}")
    print(f"   已知WGS84:  {known_wgs84}")
    
    try:
        # GZ2000 -> WGS84转换
        converted_wgs84 = converter.gz2000_to_wgs84(test_gz2000[0], test_gz2000[1])
        print(f"\n🔄 GZ2000 -> WGS84:")
        print(f"   转换结果:   {converted_wgs84}")
        
        # 计算误差
        lon_error = abs(converted_wgs84[0] - known_wgs84[0])
        lat_error = abs(converted_wgs84[1] - known_wgs84[1])
        
        print(f"   经度误差:   {lon_error:.6f}度 ({lon_error * 96000:.1f}米)")
        print(f"   纬度误差:   {lat_error:.6f}度 ({lat_error * 111000:.1f}米)")
        
        # WGS84 -> GZ2000转换
        converted_gz2000 = converter.wgs84_to_gz2000(known_wgs84[0], known_wgs84[1])
        print(f"\n🔄 WGS84 -> GZ2000:")
        print(f"   转换结果:   {converted_gz2000}")
        
        x_error = abs(converted_gz2000[0] - test_gz2000[0])
        y_error = abs(converted_gz2000[1] - test_gz2000[1])
        
        print(f"   X坐标误差:  {x_error:.1f}米")
        print(f"   Y坐标误差:  {y_error:.1f}米")
        
    except Exception as e:
        print(f"❌ 转换测试失败: {e}")


def convert_village_coordinates_precise():
    """使用精确转换转换村居坐标"""
    
    print(f"\n🏘️  村居坐标精确转换")
    print("=" * 40)
    
    try:
        converter = PreciseCoordinateConverter()
    except Exception as e:
        print(f"❌ 转换器初始化失败: {e}")
        return
    
    # 加载村居数据
    try:
        with open('cunju.json', 'r', encoding='utf-8') as f:
            cunju_data = json.load(f)
    except Exception as e:
        print(f"❌ 加载村居数据失败: {e}")
        return
    
    features = cunju_data['recordsets'][0]['features']
    
    print(f"📊 转换前5个村居的坐标:")
    print("-" * 70)
    
    for i, feature in enumerate(features[:5]):
        field_names = feature['fieldNames']
        field_values = feature['fieldValues']
        village_info = dict(zip(field_names, field_values))
        
        center = feature['geometry']['center']
        gz2000_coords = (center['x'], center['y'])
        
        try:
            # 转换为WGS84
            wgs84_coords = converter.gz2000_to_wgs84(gz2000_coords[0], gz2000_coords[1])
            
            print(f"{i+1}. {village_info['NAME']}")
            print(f"   GZ2000: ({gz2000_coords[0]:.2f}, {gz2000_coords[1]:.2f})")
            print(f"   WGS84:  ({wgs84_coords[0]:.6f}, {wgs84_coords[1]:.6f})")
            print()
            
        except Exception as e:
            print(f"{i+1}. {village_info['NAME']} - 转换失败: {e}")


def export_wgs84_coordinates():
    """导出所有村居的WGS84坐标"""
    
    print(f"\n📤 导出WGS84坐标")
    print("=" * 30)
    
    try:
        converter = PreciseCoordinateConverter()
    except Exception as e:
        print(f"❌ 转换器初始化失败: {e}")
        return
    
    # 加载村居数据
    try:
        with open('cunju.json', 'r', encoding='utf-8') as f:
            cunju_data = json.load(f)
    except Exception as e:
        print(f"❌ 加载村居数据失败: {e}")
        return
    
    features = cunju_data['recordsets'][0]['features']
    wgs84_data = {}
    
    print(f"🔄 正在转换 {len(features)} 个村居坐标...")
    
    success_count = 0
    error_count = 0
    
    for feature in features:
        field_names = feature['fieldNames']
        field_values = feature['fieldValues']
        village_info = dict(zip(field_names, field_values))
        
        center = feature['geometry']['center']
        gz2000_coords = (center['x'], center['y'])
        
        try:
            # 转换为WGS84
            wgs84_coords = converter.gz2000_to_wgs84(gz2000_coords[0], gz2000_coords[1])
            
            wgs84_data[village_info['XZQH']] = {
                'name': village_info['NAME'],
                'street': village_info['PNAME'],
                'district': village_info['PPNAME'],
                'gz2000': {
                    'x': gz2000_coords[0],
                    'y': gz2000_coords[1]
                },
                'wgs84': {
                    'longitude': wgs84_coords[0],
                    'latitude': wgs84_coords[1]
                }
            }
            success_count += 1
            
        except Exception as e:
            print(f"⚠️  {village_info['NAME']} 转换失败: {e}")
            error_count += 1
    
    # 保存结果
    output_file = "villages_wgs84.json"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(wgs84_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 转换完成:")
        print(f"   成功: {success_count} 个村居")
        print(f"   失败: {error_count} 个村居")
        print(f"   输出文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")


def interactive_converter():
    """交互式转换工具"""
    
    print(f"\n🛠️  交互式坐标转换工具")
    print("=" * 40)
    
    try:
        converter = PreciseCoordinateConverter()
    except Exception as e:
        print(f"❌ 转换器初始化失败: {e}")
        return
    
    while True:
        print(f"\n选择转换方向:")
        print("1. GZ2000 -> WGS84")
        print("2. WGS84 -> GZ2000")
        print("3. 退出")
        
        choice = input("请选择 (1-3): ").strip()
        
        if choice == '1':
            try:
                x = float(input("请输入GZ2000 X坐标 (米): "))
                y = float(input("请输入GZ2000 Y坐标 (米): "))
                
                lon, lat = converter.gz2000_to_wgs84(x, y)
                print(f"✅ 转换结果: 经度={lon:.8f}, 纬度={lat:.8f}")
                
            except ValueError:
                print("❌ 输入格式错误")
            except Exception as e:
                print(f"❌ 转换失败: {e}")
                
        elif choice == '2':
            try:
                lon = float(input("请输入WGS84经度: "))
                lat = float(input("请输入WGS84纬度: "))
                
                x, y = converter.wgs84_to_gz2000(lon, lat)
                print(f"✅ 转换结果: X={x:.2f}米, Y={y:.2f}米")
                
            except ValueError:
                print("❌ 输入格式错误")
            except Exception as e:
                print(f"❌ 转换失败: {e}")
                
        elif choice == '3':
            print("👋 退出转换工具")
            break
        else:
            print("❌ 无效选择")


def main():
    """主函数"""
    
    parser = argparse.ArgumentParser(description='精确坐标转换工具')
    parser.add_argument('--test', action='store_true', help='运行转换测试')
    parser.add_argument('--convert-villages', action='store_true', help='转换村居坐标示例')
    parser.add_argument('--export', action='store_true', help='导出所有村居WGS84坐标')
    parser.add_argument('--interactive', action='store_true', help='交互式转换')
    
    args = parser.parse_args()
    
    print("🌍 精确坐标转换工具 (基于pyproj)")
    print("=" * 50)
    
    if not PYPROJ_AVAILABLE:
        print("❌ 需要安装pyproj库:")
        print("   pip install pyproj")
        return
    
    if args.test:
        test_precise_conversion()
    
    if args.convert_villages:
        convert_village_coordinates_precise()
    
    if args.export:
        export_wgs84_coordinates()
    
    if args.interactive:
        interactive_converter()
    
    if not any([args.test, args.convert_villages, args.export, args.interactive]):
        # 默认运行测试
        test_precise_conversion()
        convert_village_coordinates_precise()


if __name__ == "__main__":
    main()
