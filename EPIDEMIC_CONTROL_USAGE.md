# 村居分级防控管理系统使用指南

## 🎯 系统概述

村居分级防控管理系统是基于地理位置和相邻关系的疫情防控分级管理工具，严格按照防控要求实现：

- **一类村居**: 出现本地病例或300米警戒区内的村居
- **二类村居**: 与疫点相邻的村居  
- **三类村居**: 未出现上述情形的村居
- **非涉疫村居**: 需要重点防控的其他村居

## 📊 演示结果

通过系统演示，成功验证了完整的防控流程：

### 🦠 疫点影响分析
- **疫点**: 东平北路疫点 (42572.58, 243235.79)
- **一类村居**: 3个 (平中社区、东恒社区、均禾社区)
- **二类村居**: 6个 (集贤苑社区、蝶云天社区等)
- **覆盖范围**: 300米警戒区精确计算

### 📈 风险监测与升级
- **风险更新**: 平中社区升级为高风险
- **自动升级**: 集贤苑社区因连续2次高风险从二类升级为一类
- **防控调整**: 高风险村居消杀频率自动调整为每日

## 🚀 快速开始

### 1. 系统演示
```bash
# 运行完整演示
python epidemic_control_demo.py
```

### 2. 命令行操作

#### 添加疫点
```bash
# 添加本地疫点
python epidemic_control_cli.py add-point \
  --id EP001 \
  --name "某小区疫点" \
  --address "广州市白云区东平北路11号" \
  --local

# 使用坐标添加疫点
python epidemic_control_cli.py add-point \
  --id EP002 \
  --name "某办公楼疫点" \
  --coordinates "42000,243000" \
  --local
```

#### 更新村居风险
```bash
# 更新村居风险等级
python epidemic_control_cli.py update-risk \
  --village-name "平中社区" \
  --risk high \
  --notes "监测发现蚊媒密度较高"

# 通过村居ID更新
python epidemic_control_cli.py update-risk \
  --village-id "440111011003" \
  --risk medium \
  --source "自动监测"
```

#### 查询村居状态
```bash
# 查询单个村居
python epidemic_control_cli.py query --village-name "平中社区"

# 查询指定分级的村居
python epidemic_control_cli.py query --level 1  # 一类村居
python epidemic_control_cli.py query --level 2  # 二类村居

# 查看总体统计
python epidemic_control_cli.py query
```

#### 高风险村居监控
```bash
# 显示所有高风险村居
python epidemic_control_cli.py high-risk
```

#### 数据导出
```bash
# 导出防控数据
python epidemic_control_cli.py export --output control_data.json
```

## 📋 防控措施标准

### 一类村居防控措施
- **监测频率**: 每日开展蚊媒监测
- **消杀频率**: 
  - 前5天：全面彻底消杀（合围式推进）
  - 低风险：每2天一次
  - 中高风险：每天开展
- **特殊要求**: 
  - 应由外往内合围式推进
  - 9天后根据风险等级调整频率
- **防控周期**: 至防控周期结束

### 二类村居防控措施
- **监测频率**: 每2天开展一次蚊媒监测
- **消杀频率**:
  - 低风险：每3天一次
  - 中高风险：立即开展
- **防控周期**: 至邻近区域防控周期结束

### 三类村居防控措施
- **监测频率**: 每3天开展一次蚊媒监测
- **消杀频率**:
  - 低风险：每周一次
  - 中高风险：立即开展
- **防控周期**: 持续监测

## 🔄 分级调整规则

### 升级条件
- **连续2次高风险** → 提升一级
- **连续3次中风险** → 提升一级

### 降级条件  
- **连续2次风险可控** → 下降一级

### 调整示例
```
三类村居 → 二类村居 → 一类村居
   ↑           ↑           ↑
连续风险     连续风险     已是最高级
```

## 📊 数据结构

### 疫点数据
```json
{
  "id": "EP001",
  "name": "东平北路疫点", 
  "address": "广州市白云区东平北路11号",
  "coordinates": [42572.58, 243235.79],
  "report_date": "2025-08-05",
  "is_local": true,
  "is_active": true
}
```

### 村居状态数据
```json
{
  "village_id": "440111011003",
  "village_name": "平中社区",
  "current_level": "一类村居",
  "current_risk": "高风险", 
  "affected_by_points": ["EP001"],
  "control_start_date": "2025-08-05",
  "risk_history": [
    {
      "date": "2025-08-05",
      "risk_level": "高风险",
      "monitoring_data": {...},
      "notes": "监测发现蚊媒密度较高"
    }
  ]
}
```

## 🎯 核心算法

### 300米警戒区计算
- 使用圆周采样法，在300米圆周上生成360个采样点
- 统计每个村居被采样到的点数
- 有采样点命中的村居自动划为一类村居

### 相邻关系识别
- 基于村居多边形的几何相邻关系
- 使用touches()和intersects()方法判断
- 自动识别与一类村居相邻的二类村居

### 风险评估与升级
- 记录每次风险监测结果
- 自动判断连续风险情况
- 触发升级/降级规则

## 📈 系统优势

1. **精确地理计算**: 基于GZ2000坐标系的精确距离计算
2. **自动分级管理**: 根据疫点自动划分村居等级
3. **智能风险评估**: 连续监测数据的智能分析
4. **灵活防控措施**: 根据分级和风险自动调整措施
5. **完整数据追踪**: 详细的历史记录和状态变更
6. **标准化流程**: 严格按照防控要求实施

## 🔧 技术架构

- **地理计算**: 基于Shapely的几何计算
- **数据管理**: JSON格式的结构化存储
- **相邻分析**: 基于多边形拓扑关系
- **命令行接口**: 便于自动化和脚本调用
- **可视化输出**: 清晰的状态展示和统计报告

## 📞 使用支持

系统提供完整的错误处理和用户友好提示。如遇问题，请检查：
1. 村居数据文件是否完整
2. 地址查询是否成功
3. 坐标格式是否正确
4. 风险等级参数是否有效

通过本系统，可以实现标准化、自动化、智能化的村居分级防控管理！🦠🛡️
