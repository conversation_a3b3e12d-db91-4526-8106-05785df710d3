#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的命令行工具 - 查询地址覆盖范围
使用方法: python query_coverage.py "地址" [半径]
"""

import sys
import argparse
from geo_coverage_calculator import GeoCoverageCalculator


def main():
    """命令行主函数"""
    
    # 设置命令行参数解析
    parser = argparse.ArgumentParser(
        description='查询指定地址周围指定半径范围内覆盖的村居',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python query_coverage.py "东平北路 11 号"
  python query_coverage.py "东平北路 11 号" --radius 500
  python query_coverage.py "白云区三元里大道" -r 1000
  python query_coverage.py --interactive
        """
    )
    
    parser.add_argument(
        'address',
        nargs='?',
        help='要查询的地址'
    )
    
    parser.add_argument(
        '-r', '--radius',
        type=int,
        default=300,
        help='覆盖半径（米），默认300米'
    )
    
    parser.add_argument(
        '-i', '--interactive',
        action='store_true',
        help='启动交互模式'
    )
    
    parser.add_argument(
        '--simple',
        action='store_true',
        help='简化输出，只显示村居名称和距离'
    )
    
    args = parser.parse_args()
    
    # 创建计算器实例
    calculator = GeoCoverageCalculator()
    
    # 加载村居数据
    print("正在加载村居数据...")
    if not calculator.load_cunju_data():
        print("❌ 加载村居数据失败，程序退出")
        sys.exit(1)
    
    # 交互模式
    if args.interactive:
        interactive_mode(calculator, args.simple)
        return
    
    # 检查是否提供了地址
    if not args.address:
        print("❌ 请提供要查询的地址")
        parser.print_help()
        sys.exit(1)
    
    # 验证半径
    if args.radius <= 0:
        print("❌ 半径必须大于0")
        sys.exit(1)
    
    # 执行查询
    print(f"\n🔍 正在查询 '{args.address}' 周围 {args.radius}米 范围...")
    
    covered_villages = calculator.calculate_coverage_by_address(args.address, radius=args.radius)
    
    if not covered_villages:
        print("❌ 未找到覆盖的村居或查询失败")
        sys.exit(1)
    
    # 根据输出模式显示结果
    if args.simple:
        print_simple_result(covered_villages, args.address, args.radius)
    
    print(f"\n✅ 查询完成，共找到 {len(covered_villages)} 个覆盖的村居")


def interactive_mode(calculator, simple_output=False):
    """交互模式"""
    
    print("\n" + "="*60)
    print("🌍 地理覆盖范围计算器 - 交互模式")
    print("="*60)
    print("💡 输入地址查询覆盖范围，输入 'quit' 或 'q' 退出")
    print("💡 输入格式: 地址 [半径]，例如: 东平北路 11 号 500")
    
    while True:
        try:
            # 获取用户输入
            user_input = input("\n📍 请输入地址和半径(可选): ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 退出程序")
                break
            
            if not user_input:
                print("⚠️  请输入有效地址")
                continue
            
            # 解析输入
            parts = user_input.split()
            if len(parts) == 1:
                address = parts[0]
                radius = 300
            elif len(parts) >= 2:
                # 检查最后一个部分是否是数字（半径）
                try:
                    radius = int(parts[-1])
                    address = ' '.join(parts[:-1])
                except ValueError:
                    # 最后一个部分不是数字，整个作为地址
                    address = user_input
                    radius = 300
            else:
                address = user_input
                radius = 300
            
            if radius <= 0:
                print("⚠️  半径必须大于0，使用默认值300米")
                radius = 300
            
            # 执行查询
            print(f"\n🔍 正在查询 '{address}' 周围 {radius}米 范围...")
            covered_villages = calculator.calculate_coverage_by_address(address, radius=radius)
            
            if not covered_villages:
                print("❌ 未找到覆盖的村居或查询失败")
            else:
                if simple_output:
                    print_simple_result(covered_villages, address, radius)
                print(f"\n✅ 查询完成，共找到 {len(covered_villages)} 个覆盖的村居")
            
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")


def print_simple_result(covered_villages, address, radius):
    """简化输出结果"""
    
    print(f"\n📊 简化结果 - {address} 周围 {radius}米 覆盖的村居:")
    print("-" * 50)
    
    for i, village in enumerate(covered_villages, 1):
        info = village['village_info']
        distance = village['distance_to_center']
        coverage = village['coverage_ratio']
        
        print(f"{i:2d}. {info['NAME']:<15} | {info['PNAME']:<10} | {distance:6.0f}米 | {coverage:5.1%}")


if __name__ == "__main__":
    main()
