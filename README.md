# 地理位置覆盖范围计算器

这个工具可以通过地址查询坐标，并计算指定半径范围内覆盖的村居。

## 功能特性

- 通过地址查询GZ2000坐标系的坐标
- 计算指定点位周围300米（可自定义）范围内覆盖的村居
- 提供详细的覆盖分析结果，包括覆盖面积、覆盖比例等
- 支持批量地址查询

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本使用

```python
from geo_coverage_calculator import GeoCoverageCalculator

# 创建计算器实例
calculator = GeoCoverageCalculator()

# 加载村居数据
calculator.load_cunju_data()

# 通过地址计算覆盖范围
covered_villages = calculator.calculate_coverage_by_address("东平北路 11 号")
```

### 直接运行脚本

```bash
python geo_coverage_calculator.py
```

### 自定义覆盖半径

```python
# 计算500米范围内的覆盖
covered_villages = calculator.calculate_coverage_by_address("东平北路 11 号", radius=500)
```

### 直接使用坐标计算

```python
# 如果已知GZ2000坐标，可以直接计算
coordinates = (42572.58, 243235.79)  # GZ2000坐标
covered_villages = calculator.calculate_coverage(coordinates, radius=300)
calculator.print_coverage_result(covered_villages, coordinates)
```

## API说明

### GeoCoverageCalculator类

#### 主要方法

- `load_cunju_data()`: 加载村居数据
- `query_coordinates_by_address(address)`: 通过地址查询坐标
- `calculate_coverage(center_point, radius)`: 计算覆盖范围
- `calculate_coverage_by_address(address, radius)`: 通过地址计算覆盖范围
- `print_coverage_result(covered_villages, center_point, radius)`: 打印结果

#### 返回数据格式

覆盖的村居信息包含以下字段：

```python
{
    'village_info': {
        'NAME': '村居名称',
        'PNAME': '所属街道',
        'PPNAME': '所属区域',
        'XZQH': '行政区划代码',
        # ... 其他村居信息
    },
    'intersection_area': 123.45,  # 相交面积（平方米）
    'village_area': 1000.0,       # 村居总面积（平方米）
    'coverage_ratio': 0.12,       # 覆盖比例（0-1）
    'distance_to_center': 150.5,  # 到中心点距离（米）
    'village_center': (x, y)      # 村居中心坐标
}
```

## 示例输出

```
成功加载村居数据，共 9 个村居
查询地址: 东平北路 11 号
匹配地址: 广州市白云区东平北路11号
匹配度: 1.00
GZ2000坐标: (42572.58, 243235.79)

=== 覆盖范围分析结果 ===
中心点坐标: (42572.58, 243235.79)
覆盖半径: 300米
覆盖村居数量: 2

覆盖的村居详情:
--------------------------------------------------------------------------------
1. 飞鹅新村社区
   所属街道: 三元里街
   所属区域: 白云区
   行政区划: 440111002001
   到中心距离: 125.30米
   覆盖面积: 15234.56平方米
   村居总面积: 293764.19平方米
   覆盖比例: 5.18%
   村居中心: (37743.03, 231954.53)

2. 梓元岗社区
   所属街道: 三元里街
   所属区域: 白云区
   行政区划: 440111002002
   到中心距离: 280.45米
   覆盖面积: 8765.43平方米
   村居总面积: 245042.73平方米
   覆盖比例: 3.58%
   村居中心: (36993.32, 231959.51)
```

## 注意事项

1. 确保 `cunju.json` 文件在正确的路径下
2. 需要网络连接来调用地址查询API
3. 坐标系统使用GZ2000，适用于广州地区
4. 覆盖范围计算基于平面几何，适用于小范围区域

## 错误处理

程序包含完善的错误处理机制：
- API请求失败时会给出相应提示
- 地址未找到时会返回空结果
- 数据格式错误时会跳过有问题的记录

## 扩展功能

可以根据需要扩展以下功能：
- 支持其他坐标系
- 批量地址处理
- 结果导出为CSV/Excel
- 可视化展示覆盖范围
